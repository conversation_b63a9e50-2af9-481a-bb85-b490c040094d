"""
Enhanced verification code parser supporting multiple languages and formats.
Optimized for performance with pre-compiled regex patterns and ReDoS protection.
"""
import html
import re
import signal
import time
from dataclasses import dataclass
from typing import List, Optional, Pattern, Tuple, Dict, Set, Any


from .config import get_config
from .exceptions import SecurityError, ParsingError
from .constants import ParsingConstants, ScoringConstants, SecurityConstants
from .types import CacheStats, VerificationCodeMatch


class RegexTimeoutError(Exception):
    """正则表达式超时异常。"""
    pass


def timeout_handler(signum: int, frame: Any) -> None:
    """正则表达式超时处理器。"""
    raise RegexTimeoutError("正则表达式执行超时")


def safe_regex_search(pattern: Pattern[str], text: str, timeout: float = 1.0) -> Optional[re.Match]:
    """
    安全的正则表达式搜索，带超时保护。

    Args:
        pattern: 编译后的正则表达式模式
        text: 要搜索的文本
        timeout: 超时时间（秒）

    Returns:
        匹配结果或None

    Raises:
        SecurityError: 正则表达式执行超时
    """
    try:
        # 设置超时信号（仅在Unix系统上可用）
        if hasattr(signal, 'SIGALRM'):
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(timeout))

        start_time = time.time()
        result = pattern.search(text)

        # 检查执行时间
        if time.time() - start_time > timeout:
            raise SecurityError(f"正则表达式执行时间过长: {time.time() - start_time:.2f}秒")

        return result

    except RegexTimeoutError as e:
        raise SecurityError("正则表达式执行超时，可能存在ReDoS攻击") from e
    finally:
        # 清除超时信号
        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)


def safe_regex_finditer(pattern: Pattern[str], text: str, timeout: float = 1.0, max_matches: int = 100):
    """
    安全的正则表达式查找所有匹配，带超时和数量限制。

    Args:
        pattern: 编译后的正则表达式模式
        text: 要搜索的文本
        timeout: 超时时间（秒）
        max_matches: 最大匹配数量

    Yields:
        匹配结果

    Raises:
        SecurityError: 正则表达式执行超时或匹配数量过多
    """
    try:
        if hasattr(signal, 'SIGALRM'):
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(timeout))

        start_time = time.time()
        match_count = 0

        for match in pattern.finditer(text):
            match_count += 1

            # 检查匹配数量限制
            if match_count > max_matches:
                raise SecurityError(f"正则表达式匹配数量过多: {match_count}")

            # 检查执行时间
            if time.time() - start_time > timeout:
                raise SecurityError(f"正则表达式执行时间过长: {time.time() - start_time:.2f}秒")

            yield match

    except RegexTimeoutError as e:
        raise SecurityError("正则表达式执行超时，可能存在ReDoS攻击") from e
    finally:
        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)


@dataclass
class VerificationPattern:
    """表示验证码模式及其元数据。"""
    name: str
    pattern: Pattern[str]
    priority: int  # 优先级越高越先检查
    description: str
    is_safe: bool = True  # 是否为安全的正则表达式


class VerificationCodeParser:
    """
    增强的验证码解析器，支持多语言和多种格式。

    优化了性能，预编译了正则表达式，并提供ReDoS攻击防护。
    """

    def __init__(self):
        """初始化解析器，设置关键词和模式。"""
        self.config = get_config()
        self._setup_keywords()
        self._setup_patterns()
        self._setup_compiled_patterns()

        # 性能统计
        self._stats = {
            "total_parses": 0,
            "successful_parses": 0,
            "cache_hits": 0,
            "regex_timeouts": 0
        }

    def _setup_keywords(self) -> None:
        """设置多语言关键词用于验证码检测。"""
        # 使用类变量缓存关键词，避免重复创建
        if not hasattr(self.__class__, '_cached_keywords'):
            self.__class__._cached_keywords = {
                # 中文
                "chinese": [
                    "验证码", "验证代码", "确认码", "动态码", "安全码", "校验码",
                    "认证码", "身份验证码", "短信验证码", "邮箱验证码", "手机验证码",
                    "验证密码", "临时密码", "一次性密码", "OTP密码", "手机动态码",
                    "邮件验证码", "账户验证", "安全验证", "登录验证", "注册验证",
                    "激活码", "登录码", "注册码", "找回密码", "重置密码",
                    "身份验证", "二次验证", "双重验证", "动态密码", "临时验证码"
                ],
                # English
                "english": [
                    "verification code", "verification", "verify", "code", "passcode",
                    "pin", "token", "otp", "one-time password", "authentication code",
                    "auth code", "security code", "confirmation code", "access code",
                    "login code", "2fa code", "two-factor", "sms code", "email code",
                    "mobile code", "phone code", "authenticator", "secure code",
                    "validation code", "activation code", "reset code", "recovery code",
                    "temporary code", "dynamic code", "multi-factor", "mfa"
                ],
                # 日本語
                "japanese": [
                    "認証コード", "確認コード", "認証番号", "パスコード", "ワンタイムパスワード",
                    "セキュリティコード", "ログインコード", "二段階認証", "SMS認証"
                ],
                # 한국어
                "korean": [
                    "인증번호", "인증코드", "확인번호", "보안코드", "일회용비밀번호",
                    "로그인코드", "2단계인증", "SMS인증"
                ],
                # Español
                "spanish": [
                    "código de verificación", "código", "verificación", "código de acceso",
                    "código de seguridad", "código de confirmación", "pin", "clave"
                ],
                # Français
                "french": [
                    "code de vérification", "code", "vérification", "code d'accès",
                    "code de sécurité", "code de confirmation", "authentification"
                ],
                # Deutsch
                "german": [
                    "bestätigungscode", "verifizierungscode", "sicherheitscode",
                    "zugangscode", "authentifizierungscode", "pin"
                ],
                # Русский
                "russian": [
                    "код подтверждения", "код верификации", "код", "пароль",
                    "код безопасности", "код доступа", "пин-код"
                ],
                # Português
                "portuguese": [
                    "código de verificação", "código", "verificação", "código de acesso",
                    "código de segurança", "pin", "senha"
                ],
                # Italiano
                "italian": [
                    "codice di verifica", "codice", "verifica", "codice di accesso",
                    "codice di sicurezza", "pin", "password"
                ],
                # Nederlands
                "dutch": [
                    "verificatiecode", "bevestigingscode", "toegangscode",
                    "beveiligingscode", "pincode"
                ],
                # العربية
                "arabic": [
                    "رمز التحقق", "كود التحقق", "رمز الأمان", "كود الأمان", "رمز المرور"
                ]
            }

            # 扁平化所有关键词以便搜索，并按长度排序（长的优先）
            self.__class__._cached_all_keywords = []
            for lang_keywords in self.__class__._cached_keywords.values():
                self.__class__._cached_all_keywords.extend(lang_keywords)

            # 按长度降序排序，优先匹配长关键词
            self.__class__._cached_all_keywords.sort(key=len, reverse=True)

            # 创建关键词集合用于快速查找
            self.__class__._cached_keyword_set = {
                kw.lower() for kw in self.__class__._cached_all_keywords
            }

        self.verification_keywords = self.__class__._cached_keywords
        self.all_keywords = self.__class__._cached_all_keywords
        self.keyword_set = self.__class__._cached_keyword_set

    def _setup_patterns(self) -> None:
        """设置验证码模式，按优先级排序。"""
        # 使用类变量缓存模式，避免重复编译
        if not hasattr(self.__class__, '_cached_patterns'):
            self.__class__._cached_patterns = [
                # 高优先级：精确格式模式
                VerificationPattern(
                    name="standard_numeric",
                    pattern=re.compile(r'\b\d{4,8}\b', re.COMPILED),
                    priority=100,
                    description="标准4-8位数字验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="hyphenated_code",
                    pattern=re.compile(r'\b\d{2,4}-\d{2,4}(?:-\d{2,4})?\b', re.COMPILED),
                    priority=95,
                    description="连字符分隔的数字验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="alphanumeric_mixed",
                    pattern=re.compile(r'\b[A-Z0-9]{4,8}\b', re.COMPILED),
                    priority=90,
                    description="大写字母数字混合验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="alphanumeric_lower",
                    pattern=re.compile(r'\b[a-z0-9]{4,8}\b', re.COMPILED),
                    priority=85,
                    description="小写字母数字混合验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="bracketed_code",
                    pattern=re.compile(r'\[(\d{4,8})\]|\((\d{4,8})\)', re.COMPILED),
                    priority=85,
                    description="括号内的验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="quoted_code",
                    pattern=re.compile(r'["\'](\d{4,8})["\']', re.COMPILED),
                    priority=85,
                    description="引号内的验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="spaced_code",
                    pattern=re.compile(r'\b\d{1,2}\s+\d{1,2}\s+\d{1,2}(?:\s+\d{1,2})?\b', re.COMPILED),
                    priority=80,
                    description="空格分隔的数字验证码",
                    is_safe=True
                ),
                # 中等优先级：基于上下文的模式
                VerificationPattern(
                    name="is_pattern",
                    pattern=re.compile(r'\bis\s+(\d{4,8})\b', re.IGNORECASE | re.COMPILED),
                    priority=75,
                    description="'is'后面的验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="colon_separated",
                    pattern=re.compile(r':\s*(\d{4,8})\b', re.COMPILED),
                    priority=70,
                    description="冒号后的验证码",
                    is_safe=True
                ),
                # 扩展格式：更多验证码变体
                VerificationPattern(
                    name="uuid_format",
                    pattern=re.compile(r'\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\b'),
                    priority=85,
                    description="UUID格式验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="base64_like",
                    pattern=re.compile(r'\b[A-Za-z0-9+/]{16,}={0,2}\b'),
                    priority=80,
                    description="Base64格式验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="hex_format",
                    pattern=re.compile(r'\b[0-9a-fA-F]{8,32}\b'),
                    priority=75,
                    description="十六进制格式验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="dotted_numeric",
                    pattern=re.compile(r'\b\d{2,4}\.\d{2,4}\.\d{2,4}\b'),
                    priority=70,
                    description="点分隔数字验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="underscore_separated",
                    pattern=re.compile(r'\b[A-Za-z0-9]{2,8}_[A-Za-z0-9]{2,8}(?:_[A-Za-z0-9]{2,8})?\b'),
                    priority=65,
                    description="下划线分隔验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="mixed_case_alphanumeric",
                    pattern=re.compile(r'\b[A-Za-z0-9]{6,12}\b'),
                    priority=60,
                    description="混合大小写字母数字验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="special_char_code",
                    pattern=re.compile(r'\b[A-Za-z0-9@#$%^&*]{6,16}\b'),
                    priority=55,
                    description="包含特殊字符的验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="bracketed_code",
                    pattern=re.compile(r'\[([A-Za-z0-9]{4,12})\]'),
                    priority=50,
                    description="方括号包围的验证码",
                    is_safe=True
                ),
                VerificationPattern(
                    name="parentheses_code",
                    pattern=re.compile(r'\(([A-Za-z0-9]{4,12})\)'),
                    priority=45,
                    description="圆括号包围的验证码",
                    is_safe=True
                ),
                # 低优先级：更宽泛的模式
                VerificationPattern(
                    name="long_numeric",
                    pattern=re.compile(r'\b\d{9,12}\b', re.COMPILED),
                    priority=30,
                    description="长数字验证码（可能是电话号码）",
                    is_safe=True
                ),
                VerificationPattern(
                    name="very_long_alphanumeric",
                    pattern=re.compile(r'\b[A-Za-z0-9]{16,64}\b', re.COMPILED),
                    priority=20,
                    description="很长的字母数字字符串（令牌）",
                    is_safe=True
                )
            ]

            # 按优先级排序（最高优先级在前）
            self.__class__._cached_patterns.sort(key=lambda p: p.priority, reverse=True)

            # 设置链接模式
            self.__class__._cached_link_patterns = [
                re.compile(r'https?://[^\s"\'<>]*(?:verify|activate|confirm|validate|auth)[^\s"\'<>]*',
                          re.IGNORECASE | re.COMPILED),
                re.compile(r'https?://[^\s"\'<>]*[?&](?:code|token|verify)=[^\s"\'<>&]*',
                          re.IGNORECASE | re.COMPILED),
                re.compile(r'https?://[^\s"\'<>]*(?:verification|confirmation|authentication)[^\s"\'<>]*',
                          re.IGNORECASE | re.COMPILED)
            ]

        self.patterns = self.__class__._cached_patterns
        self.link_patterns = self.__class__._cached_link_patterns

    def _setup_compiled_patterns(self) -> None:
        """设置预编译的常用正则表达式模式。"""
        # 预编译常用的清理模式
        self.html_tag_pattern = re.compile(r'<[^>]+>', re.COMPILED)
        self.whitespace_pattern = re.compile(r'\s+', re.COMPILED)
        self.soft_break_pattern = re.compile(r'=\r?\n', re.COMPILED)
        self.format_marker_pattern = re.compile(r'[=\*_]{2,}', re.COMPILED)

        # 预编译误报检测模式
        self.false_positive_patterns = [
            re.compile(r'\b\d{4}\s*年', re.COMPILED),  # 中文年份
            re.compile(r'\b\d{1,2}:\d{2}', re.COMPILED),  # 时间格式
            re.compile(r'\b\d{4}-\d{2}-\d{2}', re.COMPILED),  # 日期格式
            re.compile(r'\b\d{10,}', re.COMPILED),  # 很长的数字
            re.compile(r'\b19\d{2}\b', re.COMPILED),  # 1900-1999年
            re.compile(r'\b20\d{2}\b', re.COMPILED),  # 2000-2099年
        ]

        # 预编译高置信度短语模式
        self.high_confidence_patterns = [
            re.compile(r'your code is', re.IGNORECASE | re.COMPILED),
            re.compile(r'verification code is', re.IGNORECASE | re.COMPILED),
            re.compile(r'code:', re.IGNORECASE | re.COMPILED),
            re.compile(r'验证码[：:]', re.COMPILED),
            re.compile(r'验证码是', re.COMPILED),
            re.compile(r'your verification code', re.IGNORECASE | re.COMPILED),
            re.compile(r'authentication code', re.IGNORECASE | re.COMPILED),
            re.compile(r'security code', re.IGNORECASE | re.COMPILED),
        ]

    def _clean_text(self, text: str) -> str:
        """清理和标准化文本以便更好地解析。"""
        if not text:
            return ""

        # HTML实体解码
        text = html.unescape(text)

        # 移除HTML标签（基本清理）
        text = self.html_tag_pattern.sub(' ', text)

        # 标准化空白字符
        text = self.whitespace_pattern.sub(' ', text)

        # 移除常见的邮件格式化标记
        text = self.soft_break_pattern.sub('', text)  # 移除软换行
        text = self.format_marker_pattern.sub(' ', text)  # 移除格式化标记

        return text.strip()

    def _clean_text_optimized(self, text: str) -> str:
        """优化的文本清理方法，使用预编译的正则表达式。"""
        if not text:
            return ""

        # 使用缓存的清理结果
        text_hash = hash(text)
        if hasattr(self, '_clean_cache') and text_hash in self._clean_cache:
            self._stats["cache_hits"] += 1
            return self._clean_cache[text_hash]

        if not hasattr(self, '_clean_cache'):
            self._clean_cache = {}

        # 执行清理
        cleaned = self._clean_text(text)

        # 缓存结果（限制缓存大小）
        if len(self._clean_cache) < ParsingConstants.CACHE_SIZE_LIMIT:
            self._clean_cache[text_hash] = cleaned

        return cleaned

    def _extract_code_from_match(self, match: re.Match, pattern_name: str) -> Optional[str]:
        """
        从正则匹配中提取验证码。

        Args:
            match: 正则匹配对象
            pattern_name: 模式名称

        Returns:
            提取的验证码或None
        """
        # 提取匹配的验证码
        if match.groups():
            # 如果有捕获组，使用第一个非空组
            code = next((g for g in match.groups() if g), None)
            if code is None:
                code = match.group(0)
        else:
            code = match.group(0)

        if not code:
            return None

        # 特殊处理不同格式的验证码
        if pattern_name in ["hyphenated_code", "spaced_code"]:
            # 移除连字符和空格得到干净的验证码
            code = re.sub(r'[-\s]', '', code)
        elif pattern_name in ["bracketed_code", "parentheses_code"]:
            # 括号包围的验证码已经通过捕获组提取了内容
            pass
        elif pattern_name == "underscore_separated":
            # 保留下划线分隔格式
            pass
        elif pattern_name == "dotted_numeric":
            # 保留点分隔格式
            pass
        elif pattern_name == "special_char_code":
            # 移除特殊字符，只保留字母数字
            code = re.sub(r'[^A-Za-z0-9]', '', code)

        return code if self._is_valid_code_format(code) else None

    def _find_keyword_contexts_optimized(self, text: str) -> List[Tuple[str, int, int]]:
        """优化的关键词上下文查找。"""
        contexts = []
        text_lower = text.lower()

        # 使用预排序的关键词列表，优先匹配长关键词
        for keyword in self.all_keywords:
            keyword_lower = keyword.lower()
            start = 0
            while True:
                pos = text_lower.find(keyword_lower, start)
                if pos == -1:
                    break

                # 提取关键词周围的上下文
                context_start = max(0, pos - ParsingConstants.CONTEXT_WINDOW_BEFORE)
                context_end = min(len(text), pos + len(keyword) + ParsingConstants.CONTEXT_WINDOW_AFTER)
                context = text[context_start:context_end]

                contexts.append((context, pos, pos + len(keyword)))
                start = pos + 1

        return contexts

    def _extract_links_safe(self, text: str, use_safe_regex: bool = True) -> List[str]:
        """
        提取验证链接。

        Args:
            text: 文本内容
            use_safe_regex: 是否使用安全的正则表达式（带超时保护）

        Returns:
            List[str]: 链接列表
        """
        links = []

        for pattern in self.link_patterns:
            try:
                if use_safe_regex:
                    matches = safe_regex_finditer(pattern, text, self.config.regex_timeout)
                else:
                    matches = pattern.finditer(text)

                for match in matches:
                    link = match.group(0)
                    if link not in links:  # 避免重复
                        links.append(link)

            except SecurityError:
                # 记录安全错误但继续处理其他模式
                continue

        return links

    def _extract_codes_from_context_safe(self, context: str, use_safe_regex: bool = True) -> List[Tuple[str, int]]:
        """
        从上下文中提取验证码。

        Args:
            context: 上下文字符串
            use_safe_regex: 是否使用安全的正则表达式（带超时保护）

        Returns:
            List[Tuple[str, int]]: (验证码, 优先级) 元组列表
        """
        codes = []

        for pattern_obj in self.patterns:
            if use_safe_regex and not pattern_obj.is_safe:
                continue  # 跳过不安全的模式

            try:
                if use_safe_regex:
                    matches = safe_regex_finditer(
                        pattern_obj.pattern,
                        context,
                        self.config.regex_timeout,
                        max_matches=ParsingConstants.MAX_REGEX_MATCHES
                    )
                else:
                    matches = pattern_obj.pattern.finditer(context)

                for match in matches:
                    # 使用辅助方法提取验证码
                    code = self._extract_code_from_match(match, pattern_obj.name)
                    if code:
                        codes.append((code, pattern_obj.priority))

            except SecurityError:
                # 记录安全错误但继续处理其他模式
                continue

        return codes

    def _is_valid_code_format(self, code: str) -> bool:
        """验证验证码格式是否有效。"""
        if not code or len(code) < ParsingConstants.MIN_CODE_LENGTH or len(code) > ParsingConstants.MAX_CODE_LENGTH:
            return False

        # 检查是否为误报
        for pattern in self.false_positive_patterns:
            try:
                if safe_regex_search(pattern, code, SecurityConstants.REGEX_TIMEOUT_FAST):  # 短超时用于误报检测
                    return False
            except SecurityError:
                continue

        return True

    def get_stats(self) -> Dict[str, Any]:
        """获取解析器性能统计。"""
        return self._stats.copy()

    def clear_cache(self) -> None:
        """清除缓存。"""
        if hasattr(self, '_clean_cache'):
            self._clean_cache.clear()

        # 重置统计信息
        self._stats = {
            "total_parses": 0,
            "successful_parses": 0,
            "cache_hits": 0,
            "regex_timeouts": 0
        }







    def _score_code(self, code: str, context: str, priority: int) -> int:
        """
        基于多种因素对潜在验证码进行评分。

        Args:
            code: 验证码
            context: 上下文
            priority: 基础优先级

        Returns:
            int: 评分
        """
        if not code:  # 处理None或空验证码
            return ScoringConstants.EMPTY_CODE_PENALTY

        score = priority

        # 长度评分
        code_length = len(code)
        if ParsingConstants.OPTIMAL_CODE_LENGTH_MIN <= code_length <= ParsingConstants.OPTIMAL_CODE_LENGTH_MAX:
            score += ScoringConstants.OPTIMAL_LENGTH_BONUS
        elif code_length == ParsingConstants.GOOD_CODE_LENGTH:
            score += ScoringConstants.GOOD_LENGTH_BONUS
        elif code_length < ParsingConstants.OPTIMAL_CODE_LENGTH_MIN or code_length > ParsingConstants.MAX_REASONABLE_CODE_LENGTH:
            score += ScoringConstants.BAD_LENGTH_PENALTY

        # 模式评分 - 更智能的格式识别
        if code.isdigit():
            score += ScoringConstants.DIGIT_ONLY_BONUS
        elif code.isalnum():
            # 字母数字混合，根据比例调整分数
            digit_ratio = sum(c.isdigit() for c in code) / len(code)
            if 0.3 <= digit_ratio <= 0.7:  # 良好的数字字母混合比例
                score += 15
            else:
                score += 5

        # 特殊格式奖励
        if '-' in code or '_' in code or '.' in code:
            score += 10  # 结构化格式奖励

        # UUID格式特殊奖励
        if len(code) == 36 and code.count('-') == 4:
            score += 20

        # Base64格式识别
        if code.endswith('=') or (len(code) % 4 == 0 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/' for c in code)):
            score += 15

        # 十六进制格式识别
        if all(c in '0123456789abcdefABCDEF' for c in code) and len(code) >= 8:
            score += 12

        # 上下文评分 - 使用预编译的高置信度模式
        for pattern in self.high_confidence_patterns:
            try:
                if safe_regex_search(pattern, context, SecurityConstants.REGEX_TIMEOUT_FAST):  # 短超时用于上下文检查
                    score += ScoringConstants.HIGH_CONFIDENCE_BONUS
                    break
            except SecurityError:
                continue

        # 避免常见误报 - 使用预编译的误报检测模式
        for pattern in self.false_positive_patterns:
            try:
                if safe_regex_search(pattern, context, SecurityConstants.REGEX_TIMEOUT_FAST):  # 短超时用于误报检测
                    score += ScoringConstants.FALSE_POSITIVE_PENALTY
                    break
            except SecurityError:
                continue

        return score

    def parse(self, text: str) -> Optional[str]:
        """
        解析邮件文本中的验证码或链接。

        Args:
            text: 邮件正文（纯文本或HTML）

        Returns:
            提取的验证码/链接，未找到时返回None
        """
        self._stats["total_parses"] += 1

        if not text:
            return None

        # 检查文本长度限制
        if len(text) > self.config.max_email_content_size:
            text = text[:self.config.max_email_content_size]

        try:
            # 清理文本
            cleaned_text = self._clean_text_optimized(text)

            # 首先尝试查找验证链接（最高优先级）
            links = self._extract_links_safe(cleaned_text)
            if links:
                self._stats["successful_parses"] += 1
                return links[0]  # 立即返回找到的第一个链接

            # 查找关键词上下文
            contexts = self._find_keyword_contexts_optimized(cleaned_text)

            # 从关键词上下文中提取和评分验证码
            all_codes = []

            for context, _, _ in contexts:
                codes = self._extract_codes_from_context_safe(context)
                for code, priority in codes:
                    score = self._score_code(code, context, priority)
                    all_codes.append((code, score, context))

            # 如果没有找到关键词上下文，在整个文本中搜索但优先级更低
            if not contexts:
                codes = self._extract_codes_from_context_safe(cleaned_text)
                for code, priority in codes:
                    # 没有关键词的验证码优先级更低
                    adjusted_priority = priority + ParsingConstants.PRIORITY_ADJUSTMENT_NO_KEYWORD
                    score = self._score_code(code, cleaned_text, adjusted_priority)
                    # 只考虑看起来像真正验证码的代码
                    if len(code) >= ParsingConstants.MIN_DIGIT_CODE_LENGTH and code.isdigit():
                        all_codes.append((code, score, cleaned_text))

            # 如果有合理分数的验证码，返回得分最高的
            if all_codes:
                all_codes.sort(key=lambda x: x[1], reverse=True)
                best_code, best_score, _ = all_codes[0]

                # 只返回正分数的验证码
                if best_score > 0:
                    self._stats["successful_parses"] += 1
                    return best_code

            return None

        except SecurityError as e:
            self._stats["regex_timeouts"] += 1
            raise ParsingError(f"解析过程中发生安全错误: {e.message}") from e
        except Exception as e:
            raise ParsingError(f"解析过程中发生错误: {str(e)}") from e

    def parse_multiple(self, text: str, max_results: int = ParsingConstants.DEFAULT_MAX_RESULTS) -> List[Tuple[str, int]]:
        """
        解析多个潜在的验证码及其分数。

        Args:
            text: 邮件正文
            max_results: 返回的最大结果数

        Returns:
            List[Tuple[str, int]]: 按分数排序的(验证码, 分数)元组列表
        """
        if not text:
            return []

        # 使用优化的清理方法
        cleaned_text = self._clean_text_optimized(text)

        # 使用优化的关键词上下文查找
        contexts = self._find_keyword_contexts_optimized(cleaned_text)

        if not contexts:
            contexts = [(cleaned_text, 0, len(cleaned_text))]

        all_codes = []
        seen_codes = set()

        for context, _, _ in contexts:
            # 使用重构后的安全提取方法
            codes = self._extract_codes_from_context_safe(context, use_safe_regex=False)
            for code, priority in codes:
                if code not in seen_codes:
                    score = self._score_code(code, context, priority)
                    all_codes.append((code, score))
                    seen_codes.add(code)

        # 按分数排序并返回顶部结果
        all_codes.sort(key=lambda x: x[1], reverse=True)
        return all_codes[:max_results]
