"""
输入验证和数据清理模块。

提供API响应验证、邮件内容清理、XSS防护等安全功能。
"""
import html
import re
import urllib.parse
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass

from .exceptions import ValidationError, SecurityError


@dataclass
class ValidationResult:
    """验证结果。"""
    is_valid: bool
    cleaned_data: Any = None
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class InputValidator:
    """输入验证器。"""
    
    # 危险的HTML标签
    DANGEROUS_TAGS = {
        'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
        'textarea', 'select', 'option', 'link', 'meta', 'style', 'base'
    }
    
    # 危险的HTML属性
    DANGEROUS_ATTRIBUTES = {
        'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
        'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
        'javascript:', 'vbscript:', 'data:', 'file:'
    }
    
    # 允许的URL协议
    ALLOWED_PROTOCOLS = {'http', 'https', 'mailto', 'tel'}
    
    def __init__(self):
        """初始化验证器。"""
        # 编译正则表达式以提高性能
        self.html_tag_pattern = re.compile(r'<[^>]+>', re.IGNORECASE)
        self.script_pattern = re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL)
        self.style_pattern = re.compile(r'<style[^>]*>.*?</style>', re.IGNORECASE | re.DOTALL)
        self.comment_pattern = re.compile(r'<!--.*?-->', re.DOTALL)
        self.url_pattern = re.compile(r'https?://[^\s<>"\']+', re.IGNORECASE)
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
    
    def validate_string(
        self, 
        value: Any, 
        field_name: str = "value",
        max_length: Optional[int] = None,
        min_length: Optional[int] = None,
        allow_empty: bool = True
    ) -> ValidationResult:
        """
        验证字符串输入。
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            max_length: 最大长度
            min_length: 最小长度
            allow_empty: 是否允许空值
            
        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        warnings = []
        
        # 类型检查
        if value is None:
            if not allow_empty:
                errors.append(f"{field_name}不能为空")
            return ValidationResult(len(errors) == 0, "", errors, warnings)
        
        if not isinstance(value, str):
            try:
                value = str(value)
                warnings.append(f"{field_name}已转换为字符串类型")
            except Exception:
                errors.append(f"{field_name}无法转换为字符串")
                return ValidationResult(False, None, errors, warnings)
        
        # 长度检查
        if min_length is not None and len(value) < min_length:
            errors.append(f"{field_name}长度不能少于{min_length}个字符")
        
        if max_length is not None and len(value) > max_length:
            errors.append(f"{field_name}长度不能超过{max_length}个字符")
            # 截断过长的字符串
            value = value[:max_length]
            warnings.append(f"{field_name}已截断到{max_length}个字符")
        
        # 空值检查
        if not allow_empty and not value.strip():
            errors.append(f"{field_name}不能为空")
        
        return ValidationResult(len(errors) == 0, value, errors, warnings)
    
    def validate_email(self, email: str) -> ValidationResult:
        """
        验证邮箱地址。
        
        Args:
            email: 邮箱地址
            
        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        
        if not email or not isinstance(email, str):
            errors.append("邮箱地址不能为空")
            return ValidationResult(False, None, errors)
        
        email = email.strip().lower()
        
        if not self.email_pattern.match(email):
            errors.append("邮箱地址格式无效")
        
        if len(email) > 254:  # RFC 5321 限制
            errors.append("邮箱地址过长")
        
        return ValidationResult(len(errors) == 0, email, errors)
    
    def validate_url(self, url: str) -> ValidationResult:
        """
        验证URL。
        
        Args:
            url: URL地址
            
        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        warnings = []
        
        if not url or not isinstance(url, str):
            errors.append("URL不能为空")
            return ValidationResult(False, None, errors)
        
        url = url.strip()
        
        try:
            parsed = urllib.parse.urlparse(url)
            
            # 检查协议
            if parsed.scheme.lower() not in self.ALLOWED_PROTOCOLS:
                errors.append(f"不支持的URL协议: {parsed.scheme}")
            
            # 检查主机名
            if parsed.scheme in ('http', 'https') and not parsed.netloc:
                errors.append("URL缺少主机名")
            
            # 检查长度
            if len(url) > 2048:
                warnings.append("URL过长，可能存在问题")
            
        except Exception as e:
            errors.append(f"URL格式无效: {str(e)}")
        
        return ValidationResult(len(errors) == 0, url, errors, warnings)
    
    def clean_html(self, html_content: str, strip_all: bool = False) -> str:
        """
        清理HTML内容，移除危险元素。
        
        Args:
            html_content: HTML内容
            strip_all: 是否移除所有HTML标签
            
        Returns:
            str: 清理后的内容
        """
        if not html_content:
            return ""
        
        # HTML解码
        content = html.unescape(html_content)
        
        if strip_all:
            # 移除所有HTML标签
            content = self.html_tag_pattern.sub('', content)
        else:
            # 移除危险的脚本和样式
            content = self.script_pattern.sub('', content)
            content = self.style_pattern.sub('', content)
            content = self.comment_pattern.sub('', content)
            
            # 移除危险的标签
            for tag in self.DANGEROUS_TAGS:
                pattern = re.compile(f'<{tag}[^>]*>.*?</{tag}>', re.IGNORECASE | re.DOTALL)
                content = pattern.sub('', content)
                
                # 移除自闭合标签
                pattern = re.compile(f'<{tag}[^>]*/?>', re.IGNORECASE)
                content = pattern.sub('', content)
        
        # 清理多余的空白字符
        content = re.sub(r'\s+', ' ', content).strip()
        
        return content
    
    def sanitize_text(self, text: str, max_length: Optional[int] = None) -> str:
        """
        清理文本内容。
        
        Args:
            text: 文本内容
            max_length: 最大长度
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除控制字符（除了常见的空白字符）
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # 标准化换行符
        text = re.sub(r'\r\n|\r', '\n', text)
        
        # 清理多余的空白字符
        text = re.sub(r'[ \t]+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 截断过长的文本
        if max_length and len(text) > max_length:
            text = text[:max_length]
        
        return text.strip()
    
    def validate_api_response(self, response_data: Any) -> ValidationResult:
        """
        验证API响应数据。
        
        Args:
            response_data: API响应数据
            
        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        warnings = []
        
        if response_data is None:
            errors.append("API响应数据为空")
            return ValidationResult(False, None, errors)
        
        if not isinstance(response_data, dict):
            errors.append("API响应数据格式错误：期望字典类型")
            return ValidationResult(False, None, errors)
        
        # 检查响应数据大小
        try:
            import json
            response_size = len(json.dumps(response_data))
            if response_size > 10 * 1024 * 1024:  # 10MB
                warnings.append("API响应数据过大")
        except Exception:
            pass
        
        return ValidationResult(len(errors) == 0, response_data, errors, warnings)
    
    def validate_message_data(self, message_data: Dict[str, Any]) -> ValidationResult:
        """
        验证消息数据。
        
        Args:
            message_data: 消息数据
            
        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        warnings = []
        cleaned_data = {}
        
        # 验证必需字段
        required_fields = ['id']
        for field in required_fields:
            if field not in message_data:
                errors.append(f"消息数据缺少必需字段: {field}")
        
        # 验证和清理各个字段
        for key, value in message_data.items():
            if key == 'id':
                result = self.validate_string(value, key, max_length=100, allow_empty=False)
                if not result.is_valid:
                    errors.extend(result.errors)
                else:
                    cleaned_data[key] = result.cleaned_data
            
            elif key in ('text', 'html'):
                if value:
                    # 清理邮件内容
                    if key == 'html':
                        cleaned_value = self.clean_html(str(value))
                    else:
                        cleaned_value = self.sanitize_text(str(value), max_length=1024*1024)  # 1MB
                    
                    cleaned_data[key] = cleaned_value
                    
                    if len(str(value)) > 1024*1024:
                        warnings.append(f"{key}内容过大，已截断")
            
            elif key in ('subject', 'from', 'to'):
                if value:
                    result = self.validate_string(value, key, max_length=1000)
                    if result.warnings:
                        warnings.extend(result.warnings)
                    cleaned_data[key] = result.cleaned_data or ""
            
            else:
                # 其他字段直接复制，但限制字符串长度
                if isinstance(value, str) and len(value) > 10000:
                    cleaned_data[key] = value[:10000]
                    warnings.append(f"{key}字段过长，已截断")
                else:
                    cleaned_data[key] = value
        
        return ValidationResult(len(errors) == 0, cleaned_data, errors, warnings)


# 全局验证器实例
_validator = None


def get_validator() -> InputValidator:
    """获取全局验证器实例。"""
    global _validator
    if _validator is None:
        _validator = InputValidator()
    return _validator


def validate_and_clean_message(message_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和清理消息数据。
    
    Args:
        message_data: 消息数据
        
    Returns:
        Dict[str, Any]: 清理后的消息数据
        
    Raises:
        ValidationError: 验证失败
        SecurityError: 安全检查失败
    """
    validator = get_validator()
    result = validator.validate_message_data(message_data)
    
    if not result.is_valid:
        raise ValidationError(
            f"消息数据验证失败: {'; '.join(result.errors)}",
            details={"errors": result.errors, "warnings": result.warnings}
        )
    
    return result.cleaned_data


def clean_email_content(content: str, content_type: str = "text") -> str:
    """
    清理邮件内容。
    
    Args:
        content: 邮件内容
        content_type: 内容类型（text/html）
        
    Returns:
        str: 清理后的内容
    """
    validator = get_validator()
    
    if content_type.lower() == "html":
        return validator.clean_html(content)
    else:
        return validator.sanitize_text(content)


def validate_url_safe(url: str) -> bool:
    """
    验证URL是否安全。
    
    Args:
        url: URL地址
        
    Returns:
        bool: 是否安全
    """
    validator = get_validator()
    result = validator.validate_url(url)
    return result.is_valid
