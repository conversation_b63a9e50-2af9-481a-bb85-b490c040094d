"""
常量定义模块。

定义项目中使用的所有常量，避免魔法数字和硬编码值。
"""

# HTTP状态码常量
class HttpStatusCode:
    """HTTP状态码常量。"""
    OK = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    TOO_MANY_REQUESTS = 429
    INTERNAL_SERVER_ERROR = 500


# 网络和API常量
class NetworkConstants:
    """网络和API相关常量。"""
    # 默认超时设置
    DEFAULT_REQUEST_TIMEOUT = 30
    DEFAULT_CONNECT_TIMEOUT = 10
    
    # 重试设置
    DEFAULT_MAX_RETRIES = 3
    DEFAULT_BACKOFF_FACTOR = 0.5
    RETRY_ATTEMPT_OFFSET = 1  # +1 因为第一次不算重试
    
    # 轮询设置
    DEFAULT_POLLING_TIMEOUT = 60
    DEFAULT_POLLING_INTERVAL = 5
    MAX_POLLING_INTERVAL = 30
    MIN_POLLING_INTERVAL = 1
    
    # 响应大小限制
    MAX_RESPONSE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_EMAIL_CONTENT_SIZE = 1024 * 1024  # 1MB


# 验证码解析常量
class ParsingConstants:
    """验证码解析相关常量。"""
    # 验证码长度限制
    MIN_CODE_LENGTH = 3
    MAX_CODE_LENGTH = 64
    OPTIMAL_CODE_LENGTH_MIN = 4
    OPTIMAL_CODE_LENGTH_MAX = 6
    GOOD_CODE_LENGTH = 8
    MAX_REASONABLE_CODE_LENGTH = 12
    
    # 上下文窗口大小
    CONTEXT_WINDOW_BEFORE = 50
    CONTEXT_WINDOW_AFTER = 100
    
    # 正则表达式安全设置
    DEFAULT_REGEX_TIMEOUT = 1.0
    FAST_REGEX_TIMEOUT = 0.1  # 用于快速检查
    MAX_REGEX_MATCHES = 100
    
    # 缓存设置
    CACHE_SIZE_LIMIT = 1000
    LRU_CACHE_SIZE = 1000
    
    # 优先级调整
    PRIORITY_ADJUSTMENT_NO_KEYWORD = -60

    # 验证码长度检查
    MIN_DIGIT_CODE_LENGTH = 4
    
    # 解析结果限制
    DEFAULT_MAX_RESULTS = 5
    MAX_PARSE_RESULTS = 20


# 验证码评分常量
class ScoringConstants:
    """验证码评分常量。"""
    # 长度评分
    OPTIMAL_LENGTH_BONUS = 20  # 4-6位的最佳长度
    GOOD_LENGTH_BONUS = 15     # 8位的良好长度
    BAD_LENGTH_PENALTY = -30   # 过短或过长的惩罚
    
    # 模式评分
    DIGIT_ONLY_BONUS = 10      # 纯数字奖励
    
    # 上下文评分
    HIGH_CONFIDENCE_BONUS = 25  # 高置信度短语奖励
    FALSE_POSITIVE_PENALTY = -50  # 误报惩罚
    
    # 基础分数
    EMPTY_CODE_PENALTY = -100   # 空验证码惩罚
    MINIMUM_VALID_SCORE = 0     # 最低有效分数


# 缓存常量
class CacheConstants:
    """缓存相关常量。"""
    # 缓存大小
    DEFAULT_CACHE_SIZE = 100
    MESSAGE_CACHE_SIZE = 100
    PARSE_CACHE_SIZE = 50  # 解析缓存使用一半的缓存大小
    
    # TTL设置
    DEFAULT_CACHE_TTL = 300  # 5分钟
    MESSAGE_CACHE_TTL = 300  # 5分钟
    PARSE_CACHE_TTL = 600    # 10分钟，解析结果缓存时间更长
    
    # 清理设置
    CACHE_CLEANUP_INTERVAL = 60  # 1分钟
    MAX_CACHE_ENTRIES = 1000


# 日志常量
class LoggingConstants:
    """日志相关常量。"""
    # 日志级别
    DEFAULT_LOG_LEVEL = "INFO"
    VALID_LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    
    # 日志格式
    DEFAULT_LOG_FORMAT = "json"
    VALID_LOG_FORMATS = ["json", "text"]
    
    # 审计日志
    ENABLE_AUDIT_LOG_DEFAULT = True


# 安全常量
class SecurityConstants:
    """安全相关常量。"""
    # 输入验证
    MAX_STRING_LENGTH = 10000
    MAX_URL_LENGTH = 2048
    MAX_EMAIL_LENGTH = 254  # RFC 5321 限制
    
    # 正则表达式安全
    REGEX_TIMEOUT_DEFAULT = 1.0
    REGEX_TIMEOUT_FAST = 0.1
    
    # 内容清理
    MAX_CONTENT_SIZE = 1024 * 1024  # 1MB
    
    # 允许的协议
    ALLOWED_URL_PROTOCOLS = {'http', 'https', 'mailto', 'tel'}


# 轮询策略常量
class PollingConstants:
    """轮询策略相关常量。"""
    # 策略类型
    STRATEGY_FIXED = "fixed"
    STRATEGY_EXPONENTIAL_BACKOFF = "exponential_backoff"
    STRATEGY_ADAPTIVE = "adaptive"
    STRATEGY_FIBONACCI = "fibonacci"
    
    # 自适应轮询
    ADAPTIVE_THRESHOLD_DEFAULT = 3
    JITTER_FACTOR = 0.1  # 10%的抖动
    
    # 斐波那契轮询
    FIBONACCI_SCALE_FACTOR = 0.1
    
    # 轮询统计
    RECENT_INTERVALS_LIMIT = 10


# 验证器常量
class ValidatorConstants:
    """验证器相关常量。"""
    # 危险HTML标签
    DANGEROUS_HTML_TAGS = {
        'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
        'textarea', 'select', 'option', 'link', 'meta', 'style', 'base'
    }
    
    # 危险HTML属性
    DANGEROUS_HTML_ATTRIBUTES = {
        'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
        'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
        'javascript:', 'vbscript:', 'data:', 'file:'
    }
    
    # 字符串验证
    DEFAULT_MAX_STRING_LENGTH = 1000
    DEFAULT_MIN_STRING_LENGTH = 0


# 性能常量
class PerformanceConstants:
    """性能相关常量。"""
    # 指标收集
    DEFAULT_METRICS_INTERVAL = 60  # 1分钟
    
    # 统计窗口
    STATS_WINDOW_SIZE = 100
    
    # 性能阈值
    SLOW_REQUEST_THRESHOLD = 5.0  # 5秒
    HIGH_MEMORY_THRESHOLD = 100 * 1024 * 1024  # 100MB


# 文件和路径常量
class FileConstants:
    """文件和路径相关常量。"""
    # 配置文件
    DEFAULT_CONFIG_FILE = "mail_verifier.json"
    
    # 日志文件
    DEFAULT_LOG_FILE = "mail_verifier.log"
    
    # 缓存文件
    DEFAULT_CACHE_DIR = ".cache"


# 环境变量常量
class EnvironmentConstants:
    """环境变量相关常量。"""
    # 环境变量前缀
    DEFAULT_ENV_PREFIX = "MAIL_TM_"
    
    # 核心环境变量
    TOKEN_ENV_VAR = "MAIL_TM_TOKEN"
    BASE_URL_ENV_VAR = "MAIL_TM_BASE_URL"
    DEBUG_ENV_VAR = "MAIL_TM_DEBUG"


# 消息索引常量
class MessageConstants:
    """消息处理相关常量。"""
    # 消息索引
    LATEST_MESSAGE_INDEX = 0  # 最新消息在列表的第一个位置
    
    # 消息字段
    MESSAGE_ID_FIELD = "id"
    MESSAGE_TEXT_FIELD = "text"
    MESSAGE_HTML_FIELD = "html"
    MESSAGE_SUBJECT_FIELD = "subject"
    MESSAGE_FROM_FIELD = "from"
    MESSAGE_TO_FIELD = "to"
    
    # 消息验证
    REQUIRED_MESSAGE_FIELDS = ["id"]
