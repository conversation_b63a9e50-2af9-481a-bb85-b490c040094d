"""
邮件验证器异常模块。

定义了所有自定义异常类型，用于更精确的错误处理和分类。
"""
from typing import Optional, Dict, Any


class MailVerifierError(Exception):
    """邮件验证器基础异常类。"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        初始化异常。
        
        Args:
            message: 错误消息
            details: 错误详细信息
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self) -> str:
        """返回异常的字符串表示。"""
        if self.details:
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            return f"{self.message} ({details_str})"
        return self.message


class ConfigurationError(MailVerifierError):
    """配置相关错误。"""
    pass


class AuthenticationError(MailVerifierError):
    """身份验证错误。"""
    
    def __init__(self, message: str = "身份验证失败", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class AuthorizationError(MailVerifierError):
    """授权错误。"""
    
    def __init__(self, message: str = "权限不足", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class RateLimitError(MailVerifierError):
    """速率限制错误。"""
    
    def __init__(
        self, 
        message: str = "请求频率超过限制", 
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.retry_after = retry_after


class NetworkError(MailVerifierError):
    """网络相关错误。"""
    
    def __init__(
        self, 
        message: str = "网络请求失败", 
        status_code: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.status_code = status_code


class TimeoutError(MailVerifierError):
    """超时错误。"""
    
    def __init__(
        self, 
        message: str = "请求超时", 
        timeout_duration: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.timeout_duration = timeout_duration


class APIError(MailVerifierError):
    """API相关错误。"""
    
    def __init__(
        self, 
        message: str = "API请求失败", 
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.status_code = status_code
        self.response_data = response_data or {}


class ValidationError(MailVerifierError):
    """数据验证错误。"""
    
    def __init__(
        self, 
        message: str = "数据验证失败", 
        field: Optional[str] = None,
        value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.field = field
        self.value = value


class ParsingError(MailVerifierError):
    """解析错误。"""
    
    def __init__(
        self, 
        message: str = "内容解析失败", 
        content_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.content_type = content_type


class SecurityError(MailVerifierError):
    """安全相关错误。"""
    
    def __init__(
        self, 
        message: str = "安全检查失败", 
        security_check: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.security_check = security_check


class ResourceError(MailVerifierError):
    """资源相关错误。"""
    
    def __init__(
        self, 
        message: str = "资源操作失败", 
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.resource_type = resource_type
        self.resource_id = resource_id


class MessageNotFoundError(ResourceError):
    """消息未找到错误。"""
    
    def __init__(
        self, 
        message_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        message = f"消息未找到: {message_id}" if message_id else "消息未找到"
        super().__init__(message, "message", message_id, details)


class VerificationCodeNotFoundError(MailVerifierError):
    """验证码未找到错误。"""
    
    def __init__(
        self, 
        message: str = "未找到验证码", 
        search_timeout: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.search_timeout = search_timeout


class RetryExhaustedError(MailVerifierError):
    """重试次数耗尽错误。"""
    
    def __init__(
        self, 
        message: str = "重试次数已耗尽", 
        max_retries: Optional[int] = None,
        last_error: Optional[Exception] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)
        self.max_retries = max_retries
        self.last_error = last_error


def classify_http_error(status_code: int, response_data: Optional[Dict[str, Any]] = None) -> MailVerifierError:
    """
    根据HTTP状态码分类错误。
    
    Args:
        status_code: HTTP状态码
        response_data: 响应数据
        
    Returns:
        MailVerifierError: 分类后的异常
    """
    details = {"status_code": status_code, "response_data": response_data}
    
    if status_code == 400:
        return ValidationError("请求参数无效", details=details)
    elif status_code == 401:
        return AuthenticationError("身份验证失败", details=details)
    elif status_code == 403:
        return AuthorizationError("权限不足", details=details)
    elif status_code == 404:
        return ResourceError("请求的资源不存在", details=details)
    elif status_code == 429:
        retry_after = None
        if response_data and "retry_after" in response_data:
            retry_after = response_data["retry_after"]
        return RateLimitError("请求频率超过限制", retry_after, details)
    elif 500 <= status_code < 600:
        return APIError("服务器内部错误", status_code, response_data, details)
    else:
        return APIError(f"HTTP错误: {status_code}", status_code, response_data, details)


def classify_network_error(error: Exception) -> MailVerifierError:
    """
    根据网络异常类型分类错误。
    
    Args:
        error: 原始异常
        
    Returns:
        MailVerifierError: 分类后的异常
    """
    error_name = type(error).__name__
    error_message = str(error)
    details = {"original_error": error_name, "original_message": error_message}
    
    # 超时相关错误
    if "timeout" in error_name.lower() or "timeout" in error_message.lower():
        return TimeoutError("网络请求超时", details=details)
    
    # 连接相关错误
    if "connection" in error_name.lower() or "connection" in error_message.lower():
        return NetworkError("网络连接失败", details=details)
    
    # SSL相关错误
    if "ssl" in error_name.lower() or "certificate" in error_message.lower():
        return SecurityError("SSL证书验证失败", "ssl_verification", details)
    
    # 其他网络错误
    return NetworkError(f"网络错误: {error_message}", details=details)


def wrap_exception(func):
    """
    装饰器：包装函数异常为自定义异常类型。
    
    Args:
        func: 要包装的函数
        
    Returns:
        包装后的函数
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except MailVerifierError:
            # 已经是自定义异常，直接抛出
            raise
        except Exception as e:
            # 将其他异常包装为自定义异常
            raise MailVerifierError(f"未预期的错误: {str(e)}", {"original_error": type(e).__name__}) from e
    
    return wrapper
