"""
结构化日志模块。

提供统一的日志记录功能，支持JSON格式输出、多级别日志分类和操作审计。
"""
import logging
import sys
import time
from typing import Any, Dict, Optional, Union
from pathlib import Path

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

from .config import get_config


class MailVerifierLogger:
    """邮件验证器日志记录器。"""
    
    def __init__(self, name: str = "mail_verifier"):
        """
        初始化日志记录器。
        
        Args:
            name: 日志记录器名称
        """
        self.name = name
        self._logger = None
        self._setup_logger()
    
    def _setup_logger(self) -> None:
        """设置日志记录器。"""
        config = get_config()
        
        if STRUCTLOG_AVAILABLE and config.log_format == "json":
            self._setup_structlog()
        else:
            self._setup_standard_logger()
    
    def _setup_structlog(self) -> None:
        """设置structlog结构化日志。"""
        config = get_config()
        
        # 配置structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # 配置标准库日志
        logging.basicConfig(
            format="%(message)s",
            stream=sys.stdout,
            level=getattr(logging, config.log_level.upper())
        )
        
        self._logger = structlog.get_logger(self.name)
    
    def _setup_standard_logger(self) -> None:
        """设置标准日志记录器。"""
        config = get_config()
        
        # 创建日志记录器
        logger = logging.getLogger(self.name)
        logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 避免重复添加处理器
        if not logger.handlers:
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, config.log_level.upper()))
            
            # 创建格式化器
            if config.log_format == "json":
                formatter = JSONFormatter()
            else:
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        self._logger = logger
    
    def debug(self, message: str, **kwargs: Any) -> None:
        """记录调试信息。"""
        self._log("debug", message, **kwargs)
    
    def info(self, message: str, **kwargs: Any) -> None:
        """记录信息。"""
        self._log("info", message, **kwargs)
    
    def warning(self, message: str, **kwargs: Any) -> None:
        """记录警告。"""
        self._log("warning", message, **kwargs)
    
    def error(self, message: str, **kwargs: Any) -> None:
        """记录错误。"""
        self._log("error", message, **kwargs)
    
    def critical(self, message: str, **kwargs: Any) -> None:
        """记录严重错误。"""
        self._log("critical", message, **kwargs)
    
    def audit(self, action: str, **kwargs: Any) -> None:
        """记录审计信息。"""
        config = get_config()
        if config.enable_audit_log:
            audit_data = {
                "audit": True,
                "action": action,
                "timestamp": time.time(),
                **kwargs
            }
            self._log("info", f"AUDIT: {action}", **audit_data)
    
    def _log(self, level: str, message: str, **kwargs: Any) -> None:
        """内部日志记录方法。"""
        if STRUCTLOG_AVAILABLE and hasattr(self._logger, level):
            # 使用structlog
            getattr(self._logger, level)(message, **kwargs)
        else:
            # 使用标准日志
            log_level = getattr(logging, level.upper())
            extra = {"extra_data": kwargs} if kwargs else {}
            self._logger.log(log_level, message, extra=extra)


class JSONFormatter(logging.Formatter):
    """JSON格式化器（用于标准日志库）。"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON。"""
        import json
        
        log_data = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加额外数据
        if hasattr(record, 'extra_data') and record.extra_data:
            log_data.update(record.extra_data)
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_data, ensure_ascii=False)


class LoggerMixin:
    """日志记录器混入类。"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = get_logger(self.__class__.__name__)
    
    def log_debug(self, message: str, **kwargs: Any) -> None:
        """记录调试信息。"""
        self._logger.debug(message, **kwargs)
    
    def log_info(self, message: str, **kwargs: Any) -> None:
        """记录信息。"""
        self._logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs: Any) -> None:
        """记录警告。"""
        self._logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs: Any) -> None:
        """记录错误。"""
        self._logger.error(message, **kwargs)
    
    def log_audit(self, action: str, **kwargs: Any) -> None:
        """记录审计信息。"""
        self._logger.audit(action, **kwargs)


# 全局日志记录器实例
_loggers: Dict[str, MailVerifierLogger] = {}


def get_logger(name: str = "mail_verifier") -> MailVerifierLogger:
    """
    获取日志记录器实例。
    
    Args:
        name: 日志记录器名称
        
    Returns:
        MailVerifierLogger: 日志记录器实例
    """
    if name not in _loggers:
        _loggers[name] = MailVerifierLogger(name)
    return _loggers[name]


def setup_logging(
    level: Optional[str] = None,
    format_type: Optional[str] = None,
    enable_audit: Optional[bool] = None
) -> None:
    """
    设置全局日志配置。
    
    Args:
        level: 日志级别
        format_type: 日志格式（json/text）
        enable_audit: 是否启用审计日志
    """
    config = get_config()
    
    if level:
        config.log_level = level.upper()
    if format_type:
        config.log_format = format_type.lower()
    if enable_audit is not None:
        config.enable_audit_log = enable_audit
    
    # 重新初始化所有日志记录器
    global _loggers
    _loggers.clear()


def log_function_call(func):
    """
    装饰器：记录函数调用。
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        
        # 记录函数调用开始
        logger.debug(
            f"调用函数: {func.__name__}",
            function=func.__name__,
            module=func.__module__,
            args_count=len(args),
            kwargs_keys=list(kwargs.keys())
        )
        
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            
            # 记录函数调用成功
            duration = time.time() - start_time
            logger.debug(
                f"函数调用成功: {func.__name__}",
                function=func.__name__,
                duration=duration,
                success=True
            )
            
            return result
            
        except Exception as e:
            # 记录函数调用失败
            duration = time.time() - start_time
            logger.error(
                f"函数调用失败: {func.__name__}",
                function=func.__name__,
                duration=duration,
                error=str(e),
                error_type=type(e).__name__,
                success=False
            )
            raise
    
    return wrapper


def log_api_request(method: str, url: str, **kwargs: Any) -> None:
    """
    记录API请求。
    
    Args:
        method: HTTP方法
        url: 请求URL
        **kwargs: 其他请求信息
    """
    logger = get_logger("api")
    logger.audit(
        "api_request",
        method=method,
        url=url,
        **kwargs
    )


def log_api_response(
    method: str, 
    url: str, 
    status_code: int, 
    duration: float,
    **kwargs: Any
) -> None:
    """
    记录API响应。
    
    Args:
        method: HTTP方法
        url: 请求URL
        status_code: 响应状态码
        duration: 请求耗时
        **kwargs: 其他响应信息
    """
    logger = get_logger("api")
    logger.audit(
        "api_response",
        method=method,
        url=url,
        status_code=status_code,
        duration=duration,
        success=200 <= status_code < 400,
        **kwargs
    )
