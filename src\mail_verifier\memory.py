"""
内存使用优化模块。

提供内存使用监控、大邮件内容处理限制、内存泄漏防护等功能。
"""
import gc
import sys
import psutil
import threading
import weakref
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from functools import wraps

from .config import get_config
from .logging import get_logger
from .constants import PerformanceConstants, SecurityConstants
from .exceptions import MemoryError


@dataclass
class MemoryStats:
    """内存统计信息。"""
    total_memory: int
    available_memory: int
    used_memory: int
    memory_percent: float
    process_memory: int
    process_memory_percent: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "total_memory": self.total_memory,
            "available_memory": self.available_memory,
            "used_memory": self.used_memory,
            "memory_percent": self.memory_percent,
            "process_memory": self.process_memory,
            "process_memory_percent": self.process_memory_percent
        }


class MemoryMonitor:
    """内存监控器。"""
    
    def __init__(self, threshold: int = PerformanceConstants.HIGH_MEMORY_THRESHOLD):
        """
        初始化内存监控器。
        
        Args:
            threshold: 内存使用阈值（字节）
        """
        self.threshold = threshold
        self.logger = get_logger("memory")
        self._lock = threading.RLock()
        
        # 内存使用历史
        self._memory_history: List[MemoryStats] = []
        self._max_history_size = 100
        
        # 对象引用跟踪
        self._tracked_objects: Dict[str, weakref.WeakSet] = {}
        
        # 警告回调
        self._warning_callbacks: List[Callable[[MemoryStats], None]] = []
    
    def get_current_stats(self) -> MemoryStats:
        """获取当前内存统计信息。"""
        try:
            # 系统内存信息
            memory = psutil.virtual_memory()
            
            # 进程内存信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            stats = MemoryStats(
                total_memory=memory.total,
                available_memory=memory.available,
                used_memory=memory.used,
                memory_percent=memory.percent,
                process_memory=process_memory.rss,
                process_memory_percent=(process_memory.rss / memory.total) * 100
            )
            
            # 记录历史
            with self._lock:
                self._memory_history.append(stats)
                if len(self._memory_history) > self._max_history_size:
                    self._memory_history.pop(0)
            
            # 检查阈值
            if stats.process_memory > self.threshold:
                self._trigger_warning(stats)
            
            return stats
            
        except Exception as e:
            self.logger.error("获取内存统计失败", error=str(e))
            raise MemoryError(f"无法获取内存统计: {str(e)}")
    
    def _trigger_warning(self, stats: MemoryStats) -> None:
        """触发内存警告。"""
        self.logger.warning(
            "内存使用超过阈值",
            threshold=self.threshold,
            current_usage=stats.process_memory,
            memory_percent=stats.process_memory_percent
        )
        
        # 调用警告回调
        for callback in self._warning_callbacks:
            try:
                callback(stats)
            except Exception as e:
                self.logger.error("内存警告回调执行失败", error=str(e))
    
    def add_warning_callback(self, callback: Callable[[MemoryStats], None]) -> None:
        """添加内存警告回调。"""
        self._warning_callbacks.append(callback)
    
    def track_object(self, obj: Any, category: str = "default") -> None:
        """跟踪对象引用。"""
        with self._lock:
            if category not in self._tracked_objects:
                self._tracked_objects[category] = weakref.WeakSet()
            self._tracked_objects[category].add(obj)
    
    def get_tracked_objects_count(self) -> Dict[str, int]:
        """获取跟踪对象数量。"""
        with self._lock:
            return {
                category: len(objects)
                for category, objects in self._tracked_objects.items()
            }
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """强制垃圾回收。"""
        before_stats = self.get_current_stats()
        
        # 执行垃圾回收
        collected = {
            "generation_0": gc.collect(0),
            "generation_1": gc.collect(1),
            "generation_2": gc.collect(2)
        }
        
        after_stats = self.get_current_stats()
        
        memory_freed = before_stats.process_memory - after_stats.process_memory
        
        self.logger.info(
            "垃圾回收完成",
            collected=collected,
            memory_freed=memory_freed,
            before_memory=before_stats.process_memory,
            after_memory=after_stats.process_memory
        )
        
        return {
            **collected,
            "memory_freed": memory_freed
        }
    
    def get_memory_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取内存使用历史。"""
        with self._lock:
            history = self._memory_history.copy()
            if limit:
                history = history[-limit:]
            return [stats.to_dict() for stats in history]
    
    def check_memory_leaks(self) -> Dict[str, Any]:
        """检查潜在的内存泄漏。"""
        # 获取引用计数信息
        ref_counts = {}
        for obj_type in [str, list, dict, tuple, set]:
            ref_counts[obj_type.__name__] = len(gc.get_objects())
        
        # 获取未回收的循环引用
        unreachable = gc.collect()
        
        # 分析内存增长趋势
        growth_trend = self._analyze_memory_growth()
        
        return {
            "reference_counts": ref_counts,
            "unreachable_objects": unreachable,
            "memory_growth_trend": growth_trend,
            "tracked_objects": self.get_tracked_objects_count()
        }
    
    def _analyze_memory_growth(self) -> Dict[str, Any]:
        """分析内存增长趋势。"""
        with self._lock:
            if len(self._memory_history) < 2:
                return {"trend": "insufficient_data"}
            
            recent_stats = self._memory_history[-10:]  # 最近10次记录
            
            if len(recent_stats) < 2:
                return {"trend": "insufficient_data"}
            
            # 计算内存增长率
            start_memory = recent_stats[0].process_memory
            end_memory = recent_stats[-1].process_memory
            growth_rate = (end_memory - start_memory) / start_memory * 100
            
            # 判断趋势
            if growth_rate > 10:
                trend = "increasing"
            elif growth_rate < -10:
                trend = "decreasing"
            else:
                trend = "stable"
            
            return {
                "trend": trend,
                "growth_rate": growth_rate,
                "start_memory": start_memory,
                "end_memory": end_memory,
                "sample_count": len(recent_stats)
            }


class MemoryLimitedProcessor:
    """内存限制处理器。"""
    
    def __init__(self, max_size: int = SecurityConstants.MAX_CONTENT_SIZE):
        """
        初始化内存限制处理器。
        
        Args:
            max_size: 最大处理大小（字节）
        """
        self.max_size = max_size
        self.logger = get_logger("memory_processor")
        self.monitor = MemoryMonitor()
    
    def process_large_content(self, content: str, chunk_size: int = 1024 * 1024) -> str:
        """
        分块处理大内容。
        
        Args:
            content: 内容字符串
            chunk_size: 块大小
            
        Returns:
            str: 处理后的内容
            
        Raises:
            MemoryError: 内容过大
        """
        content_size = len(content.encode('utf-8'))
        
        if content_size > self.max_size:
            self.logger.warning(
                "内容大小超过限制",
                content_size=content_size,
                max_size=self.max_size
            )
            raise MemoryError(f"内容大小 {content_size} 超过限制 {self.max_size}")
        
        # 检查当前内存使用
        stats = self.monitor.get_current_stats()
        if stats.memory_percent > 90:
            self.logger.warning("系统内存使用率过高", memory_percent=stats.memory_percent)
            # 强制垃圾回收
            self.monitor.force_garbage_collection()
        
        # 如果内容较小，直接处理
        if content_size <= chunk_size:
            return self._process_chunk(content)
        
        # 分块处理
        self.logger.info("开始分块处理大内容", content_size=content_size, chunk_size=chunk_size)
        
        processed_chunks = []
        for i in range(0, len(content), chunk_size):
            chunk = content[i:i + chunk_size]
            processed_chunk = self._process_chunk(chunk)
            processed_chunks.append(processed_chunk)
            
            # 定期检查内存使用
            if i % (chunk_size * 10) == 0:  # 每10个块检查一次
                current_stats = self.monitor.get_current_stats()
                if current_stats.memory_percent > 85:
                    self.monitor.force_garbage_collection()
        
        return ''.join(processed_chunks)
    
    def _process_chunk(self, chunk: str) -> str:
        """
        处理单个块。
        
        Args:
            chunk: 内容块
            
        Returns:
            str: 处理后的块
        """
        # 这里可以添加具体的处理逻辑
        # 例如：清理、验证、转换等
        return chunk.strip()


def memory_limit_decorator(max_size: int = SecurityConstants.MAX_CONTENT_SIZE):
    """
    内存限制装饰器。
    
    Args:
        max_size: 最大内存使用限制
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            monitor = MemoryMonitor()
            
            # 检查执行前内存状态
            before_stats = monitor.get_current_stats()
            
            if before_stats.process_memory > max_size:
                raise MemoryError(f"内存使用 {before_stats.process_memory} 超过限制 {max_size}")
            
            try:
                result = func(*args, **kwargs)
                
                # 检查执行后内存状态
                after_stats = monitor.get_current_stats()
                memory_increase = after_stats.process_memory - before_stats.process_memory
                
                if memory_increase > max_size * 0.1:  # 如果内存增长超过限制的10%
                    monitor.force_garbage_collection()
                
                return result
                
            except MemoryError:
                # 内存错误时强制垃圾回收
                monitor.force_garbage_collection()
                raise
        
        return wrapper
    return decorator


# 全局内存监控器实例
_global_monitor: Optional[MemoryMonitor] = None
_monitor_lock = threading.Lock()


def get_memory_monitor() -> MemoryMonitor:
    """获取全局内存监控器实例。"""
    global _global_monitor
    
    if _global_monitor is None:
        with _monitor_lock:
            if _global_monitor is None:
                _global_monitor = MemoryMonitor()
    
    return _global_monitor


def check_memory_usage() -> Dict[str, Any]:
    """检查当前内存使用情况的便捷函数。"""
    monitor = get_memory_monitor()
    stats = monitor.get_current_stats()
    return stats.to_dict()


def cleanup_memory() -> Dict[str, int]:
    """清理内存的便捷函数。"""
    monitor = get_memory_monitor()
    return monitor.force_garbage_collection()
