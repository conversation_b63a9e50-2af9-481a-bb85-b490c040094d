"""
网络安全模块。

提供SSL证书验证、请求超时设置、请求签名机制等网络通信安全功能。
"""
import ssl
import time
import hmac
import hashlib
import urllib.parse
from typing import Dict, Any, Optional, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .config import get_config
from .logging import get_logger
from .constants import NetworkConstants, SecurityConstants
from .exceptions import SecurityError, NetworkError


class SecureHTTPAdapter(HTTPAdapter):
    """安全的HTTP适配器，配置SSL和重试策略。"""
    
    def __init__(self, ssl_verify: bool = True, **kwargs):
        """
        初始化安全HTTP适配器。
        
        Args:
            ssl_verify: 是否验证SSL证书
            **kwargs: 其他HTTPAdapter参数
        """
        self.ssl_verify = ssl_verify
        super().__init__(**kwargs)
        
        # 配置重试策略
        retry_strategy = Retry(
            total=NetworkConstants.DEFAULT_MAX_RETRIES,
            backoff_factor=NetworkConstants.DEFAULT_BACKOFF_FACTOR,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        self.mount("http://", HTTPAdapter(max_retries=retry_strategy))
        self.mount("https://", HTTPAdapter(max_retries=retry_strategy))
    
    def init_poolmanager(self, *args, **kwargs):
        """初始化连接池管理器，配置SSL设置。"""
        if self.ssl_verify:
            # 配置强SSL设置
            kwargs['ssl_context'] = ssl.create_default_context()
            kwargs['ssl_context'].check_hostname = True
            kwargs['ssl_context'].verify_mode = ssl.CERT_REQUIRED
            kwargs['ssl_context'].minimum_version = ssl.TLSVersion.TLSv1_2
        else:
            # 禁用SSL验证（仅用于测试）
            kwargs['ssl_context'] = ssl.create_default_context()
            kwargs['ssl_context'].check_hostname = False
            kwargs['ssl_context'].verify_mode = ssl.CERT_NONE
        
        return super().init_poolmanager(*args, **kwargs)


class RequestSigner:
    """请求签名器，用于API请求签名验证。"""
    
    def __init__(self, secret_key: Optional[str] = None):
        """
        初始化请求签名器。
        
        Args:
            secret_key: 签名密钥
        """
        self.secret_key = secret_key or ""
        self.logger = get_logger("security")
    
    def sign_request(
        self, 
        method: str, 
        url: str, 
        headers: Dict[str, str], 
        body: Optional[str] = None
    ) -> str:
        """
        对请求进行签名。
        
        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            body: 请求体
            
        Returns:
            str: 签名字符串
        """
        if not self.secret_key:
            return ""
        
        # 构建签名字符串
        timestamp = str(int(time.time()))
        parsed_url = urllib.parse.urlparse(url)
        
        # 规范化URL
        canonical_url = f"{parsed_url.path}"
        if parsed_url.query:
            canonical_url += f"?{parsed_url.query}"
        
        # 规范化头部
        canonical_headers = []
        for key in sorted(headers.keys()):
            if key.lower().startswith('x-'):
                canonical_headers.append(f"{key.lower()}:{headers[key]}")
        
        # 构建待签名字符串
        string_to_sign = "\n".join([
            method.upper(),
            canonical_url,
            "\n".join(canonical_headers),
            timestamp,
            hashlib.sha256((body or "").encode()).hexdigest()
        ])
        
        # 生成签名
        signature = hmac.new(
            self.secret_key.encode(),
            string_to_sign.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return f"{timestamp}:{signature}"
    
    def verify_signature(
        self, 
        signature: str, 
        method: str, 
        url: str, 
        headers: Dict[str, str], 
        body: Optional[str] = None,
        tolerance: int = 300
    ) -> bool:
        """
        验证请求签名。
        
        Args:
            signature: 签名字符串
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            body: 请求体
            tolerance: 时间容差（秒）
            
        Returns:
            bool: 签名是否有效
        """
        if not self.secret_key or not signature:
            return False
        
        try:
            timestamp_str, provided_signature = signature.split(":", 1)
            timestamp = int(timestamp_str)
            
            # 检查时间戳
            current_time = int(time.time())
            if abs(current_time - timestamp) > tolerance:
                self.logger.warning("签名时间戳过期", timestamp=timestamp, current=current_time)
                return False
            
            # 重新计算签名
            expected_signature = self.sign_request(method, url, headers, body)
            expected_sig_part = expected_signature.split(":", 1)[1]
            
            # 使用安全比较
            return hmac.compare_digest(provided_signature, expected_sig_part)
            
        except (ValueError, IndexError) as e:
            self.logger.warning("签名格式无效", signature=signature, error=str(e))
            return False


class SecureSession:
    """安全的HTTP会话，集成SSL验证、超时设置和请求签名。"""
    
    def __init__(
        self, 
        ssl_verify: bool = True,
        timeout: Tuple[int, int] = (NetworkConstants.DEFAULT_CONNECT_TIMEOUT, NetworkConstants.DEFAULT_REQUEST_TIMEOUT),
        secret_key: Optional[str] = None
    ):
        """
        初始化安全会话。
        
        Args:
            ssl_verify: 是否验证SSL证书
            timeout: 连接和读取超时（连接超时, 读取超时）
            secret_key: 请求签名密钥
        """
        self.session = requests.Session()
        self.ssl_verify = ssl_verify
        self.timeout = timeout
        self.signer = RequestSigner(secret_key) if secret_key else None
        self.logger = get_logger("secure_session")
        
        # 配置安全适配器
        adapter = SecureHTTPAdapter(ssl_verify=ssl_verify)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 配置默认头部
        self.session.headers.update({
            'User-Agent': 'MailVerifier/1.0 (Secure)',
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
    
    def request(
        self, 
        method: str, 
        url: str, 
        **kwargs
    ) -> requests.Response:
        """
        发送安全的HTTP请求。
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: 响应对象
            
        Raises:
            SecurityError: 安全验证失败
            NetworkError: 网络请求失败
        """
        # 设置默认超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        
        # 设置SSL验证
        kwargs['verify'] = self.ssl_verify
        
        # 准备请求头
        headers = kwargs.get('headers', {})
        body = kwargs.get('data') or kwargs.get('json')
        if isinstance(body, dict):
            import json
            body = json.dumps(body)
        
        # 添加请求签名
        if self.signer:
            signature = self.signer.sign_request(method, url, headers, body)
            if signature:
                headers['X-Signature'] = signature
                kwargs['headers'] = headers
        
        # 记录请求
        self.logger.debug(
            "发送安全请求",
            method=method,
            url=url,
            ssl_verify=self.ssl_verify,
            timeout=self.timeout,
            has_signature=bool(self.signer)
        )
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # 验证响应
            self._validate_response(response)
            
            return response
            
        except requests.exceptions.SSLError as e:
            self.logger.error("SSL验证失败", url=url, error=str(e))
            raise SecurityError(f"SSL验证失败: {str(e)}")
        except requests.exceptions.Timeout as e:
            self.logger.error("请求超时", url=url, timeout=self.timeout, error=str(e))
            raise NetworkError(f"请求超时: {str(e)}")
        except requests.exceptions.RequestException as e:
            self.logger.error("网络请求失败", url=url, error=str(e))
            raise NetworkError(f"网络请求失败: {str(e)}")
    
    def _validate_response(self, response: requests.Response) -> None:
        """
        验证响应安全性。
        
        Args:
            response: 响应对象
            
        Raises:
            SecurityError: 响应验证失败
        """
        # 检查响应大小
        content_length = response.headers.get('content-length')
        if content_length and int(content_length) > NetworkConstants.MAX_RESPONSE_SIZE:
            raise SecurityError(f"响应大小超过限制: {content_length}")
        
        # 检查内容类型
        content_type = response.headers.get('content-type', '')
        if not content_type.startswith(('application/json', 'text/', 'application/xml')):
            self.logger.warning("响应内容类型可疑", content_type=content_type)
        
        # 检查安全头部
        security_headers = [
            'strict-transport-security',
            'x-content-type-options',
            'x-frame-options',
            'x-xss-protection'
        ]
        
        missing_headers = []
        for header in security_headers:
            if header not in response.headers:
                missing_headers.append(header)
        
        if missing_headers:
            self.logger.info("响应缺少安全头部", missing_headers=missing_headers)
    
    def close(self) -> None:
        """关闭会话。"""
        self.session.close()
    
    def __enter__(self):
        """上下文管理器入口。"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口。"""
        self.close()


def create_secure_session(config: Optional[Any] = None) -> SecureSession:
    """
    创建安全会话实例。
    
    Args:
        config: 配置对象
        
    Returns:
        SecureSession: 安全会话实例
    """
    if config is None:
        config = get_config()
    
    return SecureSession(
        ssl_verify=config.ssl_verify,
        timeout=(NetworkConstants.DEFAULT_CONNECT_TIMEOUT, config.request_timeout),
        secret_key=getattr(config, 'secret_key', None)
    )
