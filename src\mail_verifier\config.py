"""
配置管理模块。

提供统一的配置管理，支持环境变量、配置文件和默认值。
包含配置验证和类型检查功能。
"""
import os
import json
from pathlib import Path
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass, field

from .constants import (
    NetworkConstants, CacheConstants, LoggingConstants,
    SecurityConstants, PerformanceConstants
)


@dataclass
class MailVerifierConfig:
    """邮件验证器配置类。"""
    
    # API配置
    api_token: str = ""
    base_url: str = "https://api.mail.tm"
    request_timeout: int = NetworkConstants.DEFAULT_REQUEST_TIMEOUT
    max_retries: int = NetworkConstants.DEFAULT_MAX_RETRIES
    retry_backoff_factor: float = NetworkConstants.DEFAULT_BACKOFF_FACTOR

    # 轮询配置
    default_polling_timeout: int = NetworkConstants.DEFAULT_POLLING_TIMEOUT
    default_polling_interval: int = NetworkConstants.DEFAULT_POLLING_INTERVAL
    max_polling_interval: int = NetworkConstants.MAX_POLLING_INTERVAL

    # 缓存配置
    enable_cache: bool = True
    cache_size: int = CacheConstants.DEFAULT_CACHE_SIZE
    cache_ttl: int = CacheConstants.DEFAULT_CACHE_TTL

    # 日志配置
    log_level: str = LoggingConstants.DEFAULT_LOG_LEVEL
    log_format: str = LoggingConstants.DEFAULT_LOG_FORMAT
    enable_audit_log: bool = LoggingConstants.ENABLE_AUDIT_LOG_DEFAULT

    # 安全配置
    ssl_verify: bool = True
    token_validation_enabled: bool = True

    # 解析器配置
    max_email_content_size: int = SecurityConstants.MAX_CONTENT_SIZE
    regex_timeout: float = SecurityConstants.REGEX_TIMEOUT_DEFAULT

    # 性能配置
    enable_performance_metrics: bool = False
    metrics_collection_interval: int = PerformanceConstants.DEFAULT_METRICS_INTERVAL
    
    # 调试配置
    debug_mode: bool = False
    verbose_logging: bool = False
    
    def __post_init__(self):
        """初始化后的验证和处理。"""
        self._validate_config()
        self._normalize_values()
    
    def _validate_config(self) -> None:
        """验证配置的有效性。"""
        errors = []
        
        # 验证API令牌
        if self.token_validation_enabled and not self.api_token:
            errors.append("API令牌不能为空")
        
        # 验证URL格式
        if not self.base_url.startswith(('http://', 'https://')):
            errors.append("base_url必须是有效的HTTP/HTTPS URL")
        
        # 验证数值范围
        if self.request_timeout <= 0:
            errors.append("request_timeout必须大于0")
        
        if self.max_retries < 0:
            errors.append("max_retries不能为负数")
        
        if self.retry_backoff_factor <= 0:
            errors.append("retry_backoff_factor必须大于0")
        
        if self.default_polling_timeout <= 0:
            errors.append("default_polling_timeout必须大于0")
        
        if self.default_polling_interval <= 0:
            errors.append("default_polling_interval必须大于0")
        
        if self.cache_size <= 0:
            errors.append("cache_size必须大于0")
        
        if self.cache_ttl <= 0:
            errors.append("cache_ttl必须大于0")
        
        if self.max_email_content_size <= 0:
            errors.append("max_email_content_size必须大于0")
        
        if self.regex_timeout <= 0:
            errors.append("regex_timeout必须大于0")
        
        # 验证日志级别
        if self.log_level.upper() not in LoggingConstants.VALID_LOG_LEVELS:
            errors.append(f"log_level必须是以下之一: {LoggingConstants.VALID_LOG_LEVELS}")

        # 验证日志格式
        if self.log_format.lower() not in LoggingConstants.VALID_LOG_FORMATS:
            errors.append(f"log_format必须是以下之一: {LoggingConstants.VALID_LOG_FORMATS}")
        
        if errors:
            raise ValueError(f"配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))
    
    def _normalize_values(self) -> None:
        """标准化配置值。"""
        # 标准化字符串值
        self.log_level = self.log_level.upper()
        self.log_format = self.log_format.lower()
        
        # 确保URL不以斜杠结尾
        self.base_url = self.base_url.rstrip('/')
        
        # 确保轮询间隔不超过最大值
        if self.default_polling_interval > self.max_polling_interval:
            self.default_polling_interval = self.max_polling_interval
    
    @classmethod
    def from_env(cls, prefix: str = "MAIL_TM_") -> "MailVerifierConfig":
        """从环境变量创建配置。
        
        Args:
            prefix: 环境变量前缀
            
        Returns:
            MailVerifierConfig: 配置实例
        """
        config_dict = {}
        
        # 环境变量映射
        env_mapping = {
            f"{prefix}TOKEN": "api_token",
            f"{prefix}BASE_URL": "base_url",
            f"{prefix}TIMEOUT": "request_timeout",
            f"{prefix}MAX_RETRIES": "max_retries",
            f"{prefix}BACKOFF_FACTOR": "retry_backoff_factor",
            f"{prefix}POLLING_TIMEOUT": "default_polling_timeout",
            f"{prefix}POLLING_INTERVAL": "default_polling_interval",
            f"{prefix}MAX_POLLING_INTERVAL": "max_polling_interval",
            f"{prefix}ENABLE_CACHE": "enable_cache",
            f"{prefix}CACHE_SIZE": "cache_size",
            f"{prefix}CACHE_TTL": "cache_ttl",
            f"{prefix}LOG_LEVEL": "log_level",
            f"{prefix}LOG_FORMAT": "log_format",
            f"{prefix}ENABLE_AUDIT_LOG": "enable_audit_log",
            f"{prefix}SSL_VERIFY": "ssl_verify",
            f"{prefix}TOKEN_VALIDATION": "token_validation_enabled",
            f"{prefix}MAX_EMAIL_SIZE": "max_email_content_size",
            f"{prefix}REGEX_TIMEOUT": "regex_timeout",
            f"{prefix}ENABLE_METRICS": "enable_performance_metrics",
            f"{prefix}METRICS_INTERVAL": "metrics_collection_interval",
            f"{prefix}DEBUG": "debug_mode",
            f"{prefix}VERBOSE": "verbose_logging",
        }
        
        for env_var, config_key in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                config_dict[config_key] = cls._convert_env_value(value, config_key)
        
        return cls(**config_dict)
    
    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> "MailVerifierConfig":
        """从配置文件创建配置。
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            MailVerifierConfig: 配置实例
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    config_dict = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {file_path.suffix}")
            
            return cls(**config_dict)
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise ValueError(f"读取配置文件失败: {e}")
    
    @staticmethod
    def _convert_env_value(value: str, key: str) -> Any:
        """转换环境变量值为适当的类型。"""
        # 布尔值转换
        bool_keys = {
            "enable_cache", "enable_audit_log", "ssl_verify", 
            "token_validation_enabled", "enable_performance_metrics",
            "debug_mode", "verbose_logging"
        }
        if key in bool_keys:
            return value.lower() in ('true', '1', 'yes', 'on')
        
        # 整数转换
        int_keys = {
            "request_timeout", "max_retries", "default_polling_timeout",
            "default_polling_interval", "max_polling_interval", "cache_size",
            "cache_ttl", "max_email_content_size", "metrics_collection_interval"
        }
        if key in int_keys:
            try:
                return int(value)
            except ValueError:
                raise ValueError(f"配置项 {key} 必须是整数，得到: {value}")
        
        # 浮点数转换
        float_keys = {"retry_backoff_factor", "regex_timeout"}
        if key in float_keys:
            try:
                return float(value)
            except ValueError:
                raise ValueError(f"配置项 {key} 必须是浮点数，得到: {value}")
        
        # 字符串值直接返回
        return value
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    def save_to_file(self, file_path: Union[str, Path]) -> None:
        """保存配置到文件。
        
        Args:
            file_path: 配置文件路径
        """
        file_path = Path(file_path)
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        config_dict = self.to_dict()
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的配置文件格式: {file_path.suffix}")
        except Exception as e:
            raise ValueError(f"保存配置文件失败: {e}")


# 全局配置实例
_global_config: Optional[MailVerifierConfig] = None


def get_config() -> MailVerifierConfig:
    """获取全局配置实例。"""
    global _global_config
    if _global_config is None:
        _global_config = load_config()
    return _global_config


def set_config(config: MailVerifierConfig) -> None:
    """设置全局配置实例。"""
    global _global_config
    _global_config = config


def load_config(
    config_file: Optional[Union[str, Path]] = None,
    env_prefix: str = "MAIL_TM_"
) -> MailVerifierConfig:
    """加载配置。
    
    优先级：配置文件 > 环境变量 > 默认值
    
    Args:
        config_file: 配置文件路径（可选）
        env_prefix: 环境变量前缀
        
    Returns:
        MailVerifierConfig: 配置实例
    """
    # 从默认值开始
    config = MailVerifierConfig()
    
    # 从环境变量覆盖
    try:
        env_config = MailVerifierConfig.from_env(env_prefix)
        # 只覆盖非默认值
        for field_name in config.__dataclass_fields__:
            env_value = getattr(env_config, field_name)
            default_value = getattr(MailVerifierConfig(), field_name)
            if env_value != default_value:
                setattr(config, field_name, env_value)
    except Exception:
        # 环境变量加载失败时继续使用默认值
        pass
    
    # 从配置文件覆盖
    if config_file:
        try:
            file_config = MailVerifierConfig.from_file(config_file)
            # 覆盖所有配置文件中的值
            for field_name in config.__dataclass_fields__:
                file_value = getattr(file_config, field_name)
                setattr(config, field_name, file_value)
        except Exception as e:
            raise ValueError(f"加载配置文件失败: {e}")
    
    return config
