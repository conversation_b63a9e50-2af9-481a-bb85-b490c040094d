"""
健康检查模块。

提供API连通性检查、令牌有效性验证和系统健康状态监控功能。
"""
import time
import psutil
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from enum import Enum

from .config import get_config
from .logging import get_logger
from .security import create_secure_session
from .exceptions import APIError, NetworkError, AuthenticationError
from .constants import NetworkConstants, PerformanceConstants


class HealthStatus(Enum):
    """健康状态枚举。"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """健康检查结果。"""
    name: str
    status: HealthStatus
    message: str
    duration: float
    timestamp: float
    details: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "duration": self.duration,
            "timestamp": self.timestamp,
            "details": self.details
        }


@dataclass
class SystemHealth:
    """系统健康状态。"""
    overall_status: HealthStatus
    checks: List[HealthCheckResult]
    timestamp: float
    uptime: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "overall_status": self.overall_status.value,
            "checks": [check.to_dict() for check in self.checks],
            "timestamp": self.timestamp,
            "uptime": self.uptime,
            "summary": {
                "total_checks": len(self.checks),
                "healthy_checks": len([c for c in self.checks if c.status == HealthStatus.HEALTHY]),
                "degraded_checks": len([c for c in self.checks if c.status == HealthStatus.DEGRADED]),
                "unhealthy_checks": len([c for c in self.checks if c.status == HealthStatus.UNHEALTHY])
            }
        }


class HealthChecker:
    """健康检查器。"""
    
    def __init__(self, base_url: str, token: str):
        """
        初始化健康检查器。
        
        Args:
            base_url: API基础URL
            token: 认证令牌
        """
        self.base_url = base_url
        self.token = token
        self.config = get_config()
        self.logger = get_logger("health")
        self.start_time = time.time()
        
        # 健康检查函数注册表
        self._checks: Dict[str, Callable[[], HealthCheckResult]] = {
            "api_connectivity": self._check_api_connectivity,
            "token_validity": self._check_token_validity,
            "system_resources": self._check_system_resources,
            "network_latency": self._check_network_latency,
            "ssl_certificate": self._check_ssl_certificate
        }
    
    def _check_api_connectivity(self) -> HealthCheckResult:
        """检查API连通性。"""
        start_time = time.time()
        
        try:
            with create_secure_session(self.config) as session:
                # 尝试访问API根路径或健康检查端点
                response = session.request("GET", f"{self.base_url}/")
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    return HealthCheckResult(
                        name="api_connectivity",
                        status=HealthStatus.HEALTHY,
                        message="API连接正常",
                        duration=duration,
                        timestamp=time.time(),
                        details={
                            "status_code": response.status_code,
                            "response_time": duration
                        }
                    )
                else:
                    return HealthCheckResult(
                        name="api_connectivity",
                        status=HealthStatus.DEGRADED,
                        message=f"API返回非200状态码: {response.status_code}",
                        duration=duration,
                        timestamp=time.time(),
                        details={
                            "status_code": response.status_code,
                            "response_time": duration
                        }
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            return HealthCheckResult(
                name="api_connectivity",
                status=HealthStatus.UNHEALTHY,
                message=f"API连接失败: {str(e)}",
                duration=duration,
                timestamp=time.time(),
                details={
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
    
    def _check_token_validity(self) -> HealthCheckResult:
        """检查令牌有效性。"""
        start_time = time.time()
        
        try:
            with create_secure_session(self.config) as session:
                session.session.headers.update({"Authorization": f"Bearer {self.token}"})
                
                # 尝试访问需要认证的端点
                response = session.request("GET", f"{self.base_url}/messages")
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    return HealthCheckResult(
                        name="token_validity",
                        status=HealthStatus.HEALTHY,
                        message="令牌有效",
                        duration=duration,
                        timestamp=time.time(),
                        details={
                            "status_code": response.status_code,
                            "authenticated": True
                        }
                    )
                elif response.status_code == 401:
                    return HealthCheckResult(
                        name="token_validity",
                        status=HealthStatus.UNHEALTHY,
                        message="令牌无效或已过期",
                        duration=duration,
                        timestamp=time.time(),
                        details={
                            "status_code": response.status_code,
                            "authenticated": False
                        }
                    )
                else:
                    return HealthCheckResult(
                        name="token_validity",
                        status=HealthStatus.DEGRADED,
                        message=f"令牌验证返回意外状态码: {response.status_code}",
                        duration=duration,
                        timestamp=time.time(),
                        details={
                            "status_code": response.status_code,
                            "authenticated": "unknown"
                        }
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            return HealthCheckResult(
                name="token_validity",
                status=HealthStatus.UNHEALTHY,
                message=f"令牌验证失败: {str(e)}",
                duration=duration,
                timestamp=time.time(),
                details={
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
    
    def _check_system_resources(self) -> HealthCheckResult:
        """检查系统资源。"""
        start_time = time.time()
        
        try:
            # 获取系统资源信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            duration = time.time() - start_time
            
            # 评估资源状态
            status = HealthStatus.HEALTHY
            messages = []
            
            if cpu_percent > 90:
                status = HealthStatus.UNHEALTHY
                messages.append(f"CPU使用率过高: {cpu_percent}%")
            elif cpu_percent > 70:
                status = HealthStatus.DEGRADED
                messages.append(f"CPU使用率较高: {cpu_percent}%")
            
            if memory.percent > 90:
                status = HealthStatus.UNHEALTHY
                messages.append(f"内存使用率过高: {memory.percent}%")
            elif memory.percent > 70:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"内存使用率较高: {memory.percent}%")
            
            if disk.percent > 95:
                status = HealthStatus.UNHEALTHY
                messages.append(f"磁盘使用率过高: {disk.percent}%")
            elif disk.percent > 85:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"磁盘使用率较高: {disk.percent}%")
            
            message = "系统资源正常" if not messages else "; ".join(messages)
            
            return HealthCheckResult(
                name="system_resources",
                status=status,
                message=message,
                duration=duration,
                timestamp=time.time(),
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available": memory.available,
                    "disk_percent": disk.percent,
                    "disk_free": disk.free
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return HealthCheckResult(
                name="system_resources",
                status=HealthStatus.UNKNOWN,
                message=f"无法获取系统资源信息: {str(e)}",
                duration=duration,
                timestamp=time.time(),
                details={
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
    
    def _check_network_latency(self) -> HealthCheckResult:
        """检查网络延迟。"""
        start_time = time.time()
        
        try:
            with create_secure_session(self.config) as session:
                # 发送简单的HEAD请求测试延迟
                response = session.request("HEAD", self.base_url)
                duration = time.time() - start_time
                
                # 评估延迟状态
                if duration < 1.0:
                    status = HealthStatus.HEALTHY
                    message = f"网络延迟正常: {duration:.3f}秒"
                elif duration < 3.0:
                    status = HealthStatus.DEGRADED
                    message = f"网络延迟较高: {duration:.3f}秒"
                else:
                    status = HealthStatus.UNHEALTHY
                    message = f"网络延迟过高: {duration:.3f}秒"
                
                return HealthCheckResult(
                    name="network_latency",
                    status=status,
                    message=message,
                    duration=duration,
                    timestamp=time.time(),
                    details={
                        "latency": duration,
                        "status_code": response.status_code
                    }
                )
                
        except Exception as e:
            duration = time.time() - start_time
            return HealthCheckResult(
                name="network_latency",
                status=HealthStatus.UNHEALTHY,
                message=f"网络延迟检查失败: {str(e)}",
                duration=duration,
                timestamp=time.time(),
                details={
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
    
    def _check_ssl_certificate(self) -> HealthCheckResult:
        """检查SSL证书。"""
        start_time = time.time()
        
        try:
            import ssl
            import socket
            from urllib.parse import urlparse
            
            parsed_url = urlparse(self.base_url)
            hostname = parsed_url.hostname
            port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
            
            if parsed_url.scheme != 'https':
                duration = time.time() - start_time
                return HealthCheckResult(
                    name="ssl_certificate",
                    status=HealthStatus.DEGRADED,
                    message="未使用HTTPS连接",
                    duration=duration,
                    timestamp=time.time(),
                    details={
                        "scheme": parsed_url.scheme,
                        "ssl_enabled": False
                    }
                )
            
            # 获取SSL证书信息
            context = ssl.create_default_context()
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
            duration = time.time() - start_time
            
            # 检查证书有效期
            import datetime
            not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
            days_until_expiry = (not_after - datetime.datetime.now()).days
            
            if days_until_expiry > 30:
                status = HealthStatus.HEALTHY
                message = f"SSL证书有效，{days_until_expiry}天后过期"
            elif days_until_expiry > 7:
                status = HealthStatus.DEGRADED
                message = f"SSL证书即将过期，{days_until_expiry}天后过期"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"SSL证书即将过期，{days_until_expiry}天后过期"
            
            return HealthCheckResult(
                name="ssl_certificate",
                status=status,
                message=message,
                duration=duration,
                timestamp=time.time(),
                details={
                    "subject": dict(x[0] for x in cert['subject']),
                    "issuer": dict(x[0] for x in cert['issuer']),
                    "not_after": cert['notAfter'],
                    "days_until_expiry": days_until_expiry
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return HealthCheckResult(
                name="ssl_certificate",
                status=HealthStatus.UNKNOWN,
                message=f"SSL证书检查失败: {str(e)}",
                duration=duration,
                timestamp=time.time(),
                details={
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
    
    def run_check(self, check_name: str) -> HealthCheckResult:
        """
        运行单个健康检查。
        
        Args:
            check_name: 检查名称
            
        Returns:
            HealthCheckResult: 检查结果
        """
        if check_name not in self._checks:
            return HealthCheckResult(
                name=check_name,
                status=HealthStatus.UNKNOWN,
                message=f"未知的健康检查: {check_name}",
                duration=0.0,
                timestamp=time.time()
            )
        
        try:
            return self._checks[check_name]()
        except Exception as e:
            return HealthCheckResult(
                name=check_name,
                status=HealthStatus.UNHEALTHY,
                message=f"健康检查执行失败: {str(e)}",
                duration=0.0,
                timestamp=time.time(),
                details={
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
    
    def run_all_checks(self) -> SystemHealth:
        """
        运行所有健康检查。
        
        Returns:
            SystemHealth: 系统健康状态
        """
        checks = []
        
        for check_name in self._checks:
            result = self.run_check(check_name)
            checks.append(result)
            
            self.logger.debug(
                "健康检查完成",
                check=check_name,
                status=result.status.value,
                duration=result.duration
            )
        
        # 计算整体状态
        overall_status = self._calculate_overall_status(checks)
        uptime = time.time() - self.start_time
        
        system_health = SystemHealth(
            overall_status=overall_status,
            checks=checks,
            timestamp=time.time(),
            uptime=uptime
        )
        
        self.logger.info(
            "系统健康检查完成",
            overall_status=overall_status.value,
            total_checks=len(checks),
            uptime=uptime
        )
        
        return system_health
    
    def _calculate_overall_status(self, checks: List[HealthCheckResult]) -> HealthStatus:
        """
        计算整体健康状态。
        
        Args:
            checks: 检查结果列表
            
        Returns:
            HealthStatus: 整体状态
        """
        if not checks:
            return HealthStatus.UNKNOWN
        
        unhealthy_count = len([c for c in checks if c.status == HealthStatus.UNHEALTHY])
        degraded_count = len([c for c in checks if c.status == HealthStatus.DEGRADED])
        
        if unhealthy_count > 0:
            return HealthStatus.UNHEALTHY
        elif degraded_count > 0:
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.HEALTHY
    
    def get_available_checks(self) -> List[str]:
        """获取可用的健康检查列表。"""
        return list(self._checks.keys())


def create_health_checker(base_url: str, token: str) -> HealthChecker:
    """
    创建健康检查器实例。
    
    Args:
        base_url: API基础URL
        token: 认证令牌
        
    Returns:
        HealthChecker: 健康检查器实例
    """
    return HealthChecker(base_url, token)
