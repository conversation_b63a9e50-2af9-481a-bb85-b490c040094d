import sys

from mail_verifier import (
    MailVerifierClient, load_config,
    ConfigurationError, AuthenticationError, NetworkError,
    VerificationCodeNotFoundError, MailVerifierError
)

# --- 使用说明 ---
# 1. 安装依赖:
#    pip install .
#
# 2. 设置环境变量:
#    在您的终端中设置 MAIL_TM_TOKEN 环境变量。
#    - Windows (CMD): set MAIL_TM_TOKEN="your-token-here"
#    - Windows (PowerShell): $env:MAIL_TM_TOKEN="your-token-here"
#    - Linux/macOS: export MAIL_TM_TOKEN="your-token-here"
#
# 3. 运行脚本:
#    python app.py
# ----------------




def main():
    """
    主函数，演示如何使用 MailVerifierClient。
    """
    try:
        # 加载配置（优先级：配置文件 > 环境变量 > 默认值）
        config = load_config()

        # 验证API令牌
        if not config.api_token:
            raise ConfigurationError(
                "MAIL_TM_TOKEN 环境变量未设置。\n"
                "请设置您的 API 令牌：\n"
                "- Windows (CMD): set MAIL_TM_TOKEN=\"your-token-here\"\n"
                "- Windows (PowerShell): $env:MAIL_TM_TOKEN=\"your-token-here\"\n"
                "- Linux/macOS: export MAIL_TM_TOKEN=\"your-token-here\""
            )

        # 初始化客户端
        client = MailVerifierClient(token=config.api_token, base_url=config.base_url)
        print("客户端初始化成功。")
        print(f"正在等待新的邮件验证码（最长等待{config.default_polling_timeout}秒）...")

        # 获取最新的验证码（使用智能轮询）
        verification_code = client.get_latest_verification_code(
            timeout=config.default_polling_timeout,
            interval=config.default_polling_interval,
            polling_strategy="exponential_backoff"  # 使用指数退避策略
        )

        print(f"\n🎉 成功获取验证码: {verification_code}")

    except ConfigurationError as e:
        print(f"配置错误: {e.message}", file=sys.stderr)
        sys.exit(1)
    except AuthenticationError as e:
        print(f"身份验证失败: {e.message}", file=sys.stderr)
        print("请检查您的API令牌是否正确。", file=sys.stderr)
        sys.exit(1)
    except NetworkError as e:
        print(f"网络连接失败: {e.message}", file=sys.stderr)
        print("请检查网络连接和API服务状态。", file=sys.stderr)
        sys.exit(1)
    except VerificationCodeNotFoundError as e:
        print(f"😔 {e.message}", file=sys.stderr)
        print("建议：增加等待时间或检查邮件是否包含验证码。", file=sys.stderr)
        sys.exit(0)  # 这不是错误，只是没找到验证码
    except MailVerifierError as e:
        print(f"邮件验证器错误: {e.message}", file=sys.stderr)
        if e.details:
            print(f"详细信息: {e.details}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"未预期的错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
