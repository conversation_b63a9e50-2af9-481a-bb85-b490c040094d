"""
智能轮询模块。

提供指数退避、自适应间隔、事件驱动等高级轮询策略。
"""
import time
import math
from typing import Callable, Optional, Any, Dict, List
from dataclasses import dataclass
from enum import Enum

from .config import get_config
from .logging import get_logger
from .exceptions import TimeoutError, MailVerifierError


class PollingStrategy(Enum):
    """轮询策略枚举。"""
    FIXED = "fixed"  # 固定间隔
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    ADAPTIVE = "adaptive"  # 自适应
    FIBONACCI = "fibonacci"  # 斐波那契序列


@dataclass
class PollingResult:
    """轮询结果。"""
    success: bool
    result: Any = None
    attempts: int = 0
    total_time: float = 0.0
    error: Optional[Exception] = None
    strategy_used: Optional[PollingStrategy] = None


class SmartPoller:
    """智能轮询器。"""
    
    def __init__(
        self,
        strategy: PollingStrategy = PollingStrategy.EXPONENTIAL_BACKOFF,
        initial_interval: float = 1.0,
        max_interval: float = 30.0,
        backoff_factor: float = 1.5,
        jitter: bool = True,
        adaptive_threshold: int = 3
    ):
        """
        初始化智能轮询器。
        
        Args:
            strategy: 轮询策略
            initial_interval: 初始间隔（秒）
            max_interval: 最大间隔（秒）
            backoff_factor: 退避因子
            jitter: 是否添加随机抖动
            adaptive_threshold: 自适应阈值
        """
        self.strategy = strategy
        self.initial_interval = initial_interval
        self.max_interval = max_interval
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.adaptive_threshold = adaptive_threshold
        
        self.logger = get_logger("poller")
        self.config = get_config()
        
        # 统计信息
        self._stats = {
            "total_polls": 0,
            "successful_polls": 0,
            "failed_polls": 0,
            "total_time": 0.0,
            "avg_interval": 0.0
        }
        
        # 自适应状态
        self._consecutive_failures = 0
        self._consecutive_successes = 0
        self._recent_intervals: List[float] = []
        self._fibonacci_cache = [1, 1]
    
    def poll(
        self,
        check_func: Callable[[], Any],
        timeout: float = 60.0,
        success_condition: Optional[Callable[[Any], bool]] = None
    ) -> PollingResult:
        """
        执行智能轮询。
        
        Args:
            check_func: 检查函数
            timeout: 超时时间（秒）
            success_condition: 成功条件函数，返回True表示成功
            
        Returns:
            PollingResult: 轮询结果
        """
        start_time = time.time()
        attempts = 0
        current_interval = self.initial_interval
        
        self.logger.info(
            "开始智能轮询",
            strategy=self.strategy.value,
            timeout=timeout,
            initial_interval=self.initial_interval
        )
        
        while time.time() - start_time < timeout:
            attempts += 1
            self._stats["total_polls"] += 1
            
            try:
                # 执行检查函数
                result = check_func()
                
                # 检查成功条件
                if success_condition is None:
                    is_success = result is not None
                else:
                    is_success = success_condition(result)
                
                if is_success:
                    elapsed = time.time() - start_time
                    self._stats["successful_polls"] += 1
                    self._stats["total_time"] += elapsed
                    self._consecutive_successes += 1
                    self._consecutive_failures = 0
                    
                    self.logger.info(
                        "轮询成功",
                        attempts=attempts,
                        elapsed=elapsed,
                        strategy=self.strategy.value
                    )
                    
                    return PollingResult(
                        success=True,
                        result=result,
                        attempts=attempts,
                        total_time=elapsed,
                        strategy_used=self.strategy
                    )
                
                # 未成功，继续轮询
                self._consecutive_failures += 1
                self._consecutive_successes = 0
                
            except Exception as e:
                self._stats["failed_polls"] += 1
                self._consecutive_failures += 1
                self._consecutive_successes = 0
                
                self.logger.warning(
                    "轮询检查函数异常",
                    attempts=attempts,
                    error=str(e),
                    error_type=type(e).__name__
                )
                
                # 某些异常不应该继续轮询
                if isinstance(e, (PermissionError, ValueError)):
                    elapsed = time.time() - start_time
                    return PollingResult(
                        success=False,
                        attempts=attempts,
                        total_time=elapsed,
                        error=e,
                        strategy_used=self.strategy
                    )
            
            # 计算下一次轮询间隔
            next_interval = self._calculate_next_interval(current_interval, attempts)
            
            # 检查是否还有足够时间进行下一次轮询
            elapsed = time.time() - start_time
            if elapsed + next_interval >= timeout:
                break
            
            self.logger.debug(
                "等待下次轮询",
                attempt=attempts,
                interval=next_interval,
                elapsed=elapsed,
                remaining=timeout - elapsed
            )
            
            # 等待
            time.sleep(next_interval)
            current_interval = next_interval
        
        # 超时
        elapsed = time.time() - start_time
        self._stats["total_time"] += elapsed
        
        self.logger.warning(
            "轮询超时",
            attempts=attempts,
            elapsed=elapsed,
            timeout=timeout
        )
        
        return PollingResult(
            success=False,
            attempts=attempts,
            total_time=elapsed,
            error=TimeoutError(f"轮询超时：{timeout}秒内未成功", timeout),
            strategy_used=self.strategy
        )
    
    def _calculate_next_interval(self, current_interval: float, attempt: int) -> float:
        """
        计算下一次轮询间隔。
        
        Args:
            current_interval: 当前间隔
            attempt: 尝试次数
            
        Returns:
            下一次间隔
        """
        if self.strategy == PollingStrategy.FIXED:
            interval = self.initial_interval
            
        elif self.strategy == PollingStrategy.EXPONENTIAL_BACKOFF:
            interval = min(
                self.initial_interval * (self.backoff_factor ** (attempt - 1)),
                self.max_interval
            )
            
        elif self.strategy == PollingStrategy.FIBONACCI:
            interval = self._get_fibonacci_interval(attempt)
            
        elif self.strategy == PollingStrategy.ADAPTIVE:
            interval = self._calculate_adaptive_interval(current_interval)
            
        else:
            interval = self.initial_interval
        
        # 添加随机抖动以避免雷群效应
        if self.jitter:
            import random
            jitter_factor = 0.1  # 10%的抖动
            jitter_amount = interval * jitter_factor * (random.random() - 0.5) * 2
            interval += jitter_amount
        
        # 确保间隔在合理范围内
        interval = max(0.1, min(interval, self.max_interval))
        
        # 记录间隔用于统计
        self._recent_intervals.append(interval)
        if len(self._recent_intervals) > 10:
            self._recent_intervals.pop(0)
        
        return interval
    
    def _get_fibonacci_interval(self, attempt: int) -> float:
        """获取斐波那契间隔。"""
        # 扩展斐波那契缓存
        while len(self._fibonacci_cache) < attempt:
            next_fib = self._fibonacci_cache[-1] + self._fibonacci_cache[-2]
            self._fibonacci_cache.append(next_fib)
        
        if attempt <= len(self._fibonacci_cache):
            fib_value = self._fibonacci_cache[attempt - 1]
        else:
            fib_value = self._fibonacci_cache[-1]
        
        # 将斐波那契数值转换为合理的间隔
        return min(self.initial_interval * fib_value * 0.1, self.max_interval)
    
    def _calculate_adaptive_interval(self, current_interval: float) -> float:
        """计算自适应间隔。"""
        # 基于连续失败次数调整
        if self._consecutive_failures >= self.adaptive_threshold:
            # 连续失败，增加间隔
            interval = min(current_interval * 1.5, self.max_interval)
        elif self._consecutive_successes >= self.adaptive_threshold:
            # 连续成功，减少间隔
            interval = max(current_interval * 0.8, self.initial_interval)
        else:
            # 保持当前间隔
            interval = current_interval
        
        return interval
    
    def get_stats(self) -> Dict[str, Any]:
        """获取轮询统计信息。"""
        total_polls = self._stats["total_polls"]
        success_rate = (
            self._stats["successful_polls"] / total_polls 
            if total_polls > 0 else 0
        )
        
        avg_time = (
            self._stats["total_time"] / self._stats["successful_polls"]
            if self._stats["successful_polls"] > 0 else 0
        )
        
        avg_interval = (
            sum(self._recent_intervals) / len(self._recent_intervals)
            if self._recent_intervals else 0
        )
        
        return {
            **self._stats,
            "success_rate": success_rate,
            "avg_time_to_success": avg_time,
            "avg_interval": avg_interval,
            "consecutive_failures": self._consecutive_failures,
            "consecutive_successes": self._consecutive_successes,
            "strategy": self.strategy.value
        }
    
    def reset_stats(self) -> None:
        """重置统计信息。"""
        self._stats = {
            "total_polls": 0,
            "successful_polls": 0,
            "failed_polls": 0,
            "total_time": 0.0,
            "avg_interval": 0.0
        }
        self._consecutive_failures = 0
        self._consecutive_successes = 0
        self._recent_intervals.clear()


def create_smart_poller(strategy_name: str = "exponential_backoff") -> SmartPoller:
    """
    创建智能轮询器。
    
    Args:
        strategy_name: 策略名称
        
    Returns:
        SmartPoller: 智能轮询器实例
    """
    config = get_config()
    
    try:
        strategy = PollingStrategy(strategy_name)
    except ValueError:
        strategy = PollingStrategy.EXPONENTIAL_BACKOFF
    
    return SmartPoller(
        strategy=strategy,
        initial_interval=config.default_polling_interval,
        max_interval=config.max_polling_interval,
        backoff_factor=config.retry_backoff_factor
    )
