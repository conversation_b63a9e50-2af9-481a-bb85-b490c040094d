import pytest
import requests_mock
from mail_verifier.client import MailVerifierClient

def test_client_initialization():
    """Tests that the client is initialized correctly."""
    token = "test-token"
    base_url = "https://api.test.com"
    client = MailVerifierClient(token=token, base_url=base_url)
    assert client.base_url == base_url
    assert client.session.headers["Authorization"] == f"Bearer {token}"

def test_get_latest_verification_code_success(requests_mock):
    """Tests successful retrieval of a verification code."""
    token = "test-token"
    base_url = "https://api.test.com"
    client = MailVerifierClient(token=token, base_url=base_url)

    # Mock the response for the messages list
    messages_response = {
        "hydra:member": [
            {"id": "msg1", "subject": "Your verification code"}
        ]
    }
    requests_mock.get(f"{base_url}/messages", json=messages_response)

    # Mock the response for the specific message
    message_detail_response = {
        "id": "msg1",
        "text": "您的验证码是：123456"
    }
    requests_mock.get(f"{base_url}/messages/msg1", json=message_detail_response)

    code = client.get_latest_verification_code()
    assert code == "123456"


def test_enhanced_verification_parsing(requests_mock):
    """Tests the enhanced verification code parsing with various formats."""
    token = "test-token"
    base_url = "https://api.test.com"
    client = MailVerifierClient(token=token, base_url=base_url)

    test_cases = [
        ("Your verification code is: 789012", "789012"),
        ("認証コード：555666", "555666"),
        ("Código de verificación: 999888", "999888"),
        ("<p>Code: <strong>777444</strong></p>", "777444"),
        ("Verification link: https://example.com/verify?token=abc123",
         "https://example.com/verify?token=abc123"),
    ]

    for i, (email_text, expected_code) in enumerate(test_cases):
        msg_id = f"msg{i+2}"

        # Mock messages list
        messages_response = {"hydra:member": [{"id": msg_id, "subject": "Test"}]}
        requests_mock.get(f"{base_url}/messages", json=messages_response)

        # Mock message detail
        message_detail_response = {"id": msg_id, "text": email_text}
        requests_mock.get(f"{base_url}/messages/{msg_id}", json=message_detail_response)

        code = client.get_latest_verification_code()
        assert code == expected_code, f"Failed for: {email_text}"

def test_get_latest_verification_code_timeout(requests_mock):
    """Tests the timeout functionality when no message with a code is found."""
    token = "test-token"
    base_url = "https://api.test.com"
    client = MailVerifierClient(token=token, base_url=base_url)

    # Mock the response for the messages list to be always empty
    requests_mock.get(f"{base_url}/messages", json={"hydra:member": []})

    code = client.get_latest_verification_code(timeout=1, interval=0)
    assert code is None
import requests

def test_get_messages_retry_logic(requests_mock, capsys):
    """Test that get_messages retries on network failure with exponential backoff."""
    base_url = "https://api.test.com"
    client = MailVerifierClient(token="fake-token", base_url=base_url)
    
    # Simulate two failures, then one success
    requests_mock.get(
        f"{base_url}/messages",
        [
            {"exc": requests.exceptions.ConnectTimeout},
            {"exc": requests.exceptions.ReadTimeout},
            {"json": {"hydra:member": [{"id": "123"}]}},
        ],
    )

    messages = client.get_messages()
    
    # Verify that the method eventually succeeded
    assert messages == [{"id": "123"}]
    
    # Verify that the retry messages were printed to stdout/stderr
    captured = capsys.readouterr()
    assert "Retrying in 0.50 seconds..." in captured.out
    assert "Retrying in 1.00 seconds..." in captured.out