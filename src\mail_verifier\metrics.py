"""
性能指标收集模块。

实现请求响应时间、成功率、错误率等关键指标的收集和统计。
"""
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from functools import wraps

from .config import get_config
from .logging import get_logger
from .constants import PerformanceConstants


@dataclass
class RequestMetric:
    """请求指标数据。"""
    method: str
    url: str
    status_code: int
    response_time: float
    request_size: int
    response_size: int
    timestamp: float
    success: bool
    error_type: Optional[str] = None


@dataclass
class PerformanceStats:
    """性能统计数据。"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    total_request_size: int = 0
    total_response_size: int = 0
    error_counts: Dict[str, int] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """成功率。"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def error_rate(self) -> float:
        """错误率。"""
        if self.total_requests == 0:
            return 0.0
        return self.failed_requests / self.total_requests
    
    @property
    def avg_response_time(self) -> float:
        """平均响应时间。"""
        if self.total_requests == 0:
            return 0.0
        return self.total_response_time / self.total_requests
    
    @property
    def avg_request_size(self) -> float:
        """平均请求大小。"""
        if self.total_requests == 0:
            return 0.0
        return self.total_request_size / self.total_requests
    
    @property
    def avg_response_size(self) -> float:
        """平均响应大小。"""
        if self.total_requests == 0:
            return 0.0
        return self.total_response_size / self.total_requests
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.success_rate,
            "error_rate": self.error_rate,
            "avg_response_time": self.avg_response_time,
            "min_response_time": self.min_response_time if self.min_response_time != float('inf') else 0.0,
            "max_response_time": self.max_response_time,
            "avg_request_size": self.avg_request_size,
            "avg_response_size": self.avg_response_size,
            "total_request_size": self.total_request_size,
            "total_response_size": self.total_response_size,
            "error_counts": dict(self.error_counts)
        }


class MetricsCollector:
    """性能指标收集器。"""
    
    def __init__(self, window_size: int = PerformanceConstants.STATS_WINDOW_SIZE):
        """
        初始化指标收集器。
        
        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.logger = get_logger("metrics")
        self._lock = threading.RLock()
        
        # 存储最近的请求指标
        self._recent_metrics: deque = deque(maxlen=window_size)
        
        # 全局统计
        self._global_stats = PerformanceStats()
        
        # 按端点分组的统计
        self._endpoint_stats: Dict[str, PerformanceStats] = defaultdict(PerformanceStats)
        
        # 时间窗口统计
        self._window_stats = PerformanceStats()
        
        # 启动时间
        self._start_time = time.time()
    
    def record_request(self, metric: RequestMetric) -> None:
        """
        记录请求指标。
        
        Args:
            metric: 请求指标
        """
        with self._lock:
            # 添加到最近指标队列
            self._recent_metrics.append(metric)
            
            # 更新全局统计
            self._update_stats(self._global_stats, metric)
            
            # 更新端点统计
            endpoint_key = f"{metric.method} {metric.url}"
            self._update_stats(self._endpoint_stats[endpoint_key], metric)
            
            # 重新计算窗口统计
            self._recalculate_window_stats()
            
            self.logger.debug(
                "记录请求指标",
                method=metric.method,
                url=metric.url,
                status_code=metric.status_code,
                response_time=metric.response_time,
                success=metric.success
            )
    
    def _update_stats(self, stats: PerformanceStats, metric: RequestMetric) -> None:
        """
        更新统计数据。
        
        Args:
            stats: 统计对象
            metric: 请求指标
        """
        stats.total_requests += 1
        stats.total_response_time += metric.response_time
        stats.total_request_size += metric.request_size
        stats.total_response_size += metric.response_size
        
        if metric.response_time < stats.min_response_time:
            stats.min_response_time = metric.response_time
        if metric.response_time > stats.max_response_time:
            stats.max_response_time = metric.response_time
        
        if metric.success:
            stats.successful_requests += 1
        else:
            stats.failed_requests += 1
            if metric.error_type:
                stats.error_counts[metric.error_type] = stats.error_counts.get(metric.error_type, 0) + 1
    
    def _recalculate_window_stats(self) -> None:
        """重新计算窗口统计。"""
        self._window_stats = PerformanceStats()
        
        for metric in self._recent_metrics:
            self._update_stats(self._window_stats, metric)
    
    def get_global_stats(self) -> Dict[str, Any]:
        """
        获取全局统计数据。
        
        Returns:
            Dict[str, Any]: 全局统计
        """
        with self._lock:
            stats = self._global_stats.to_dict()
            stats["uptime"] = time.time() - self._start_time
            return stats
    
    def get_window_stats(self) -> Dict[str, Any]:
        """
        获取窗口统计数据。
        
        Returns:
            Dict[str, Any]: 窗口统计
        """
        with self._lock:
            stats = self._window_stats.to_dict()
            stats["window_size"] = len(self._recent_metrics)
            stats["max_window_size"] = self.window_size
            return stats
    
    def get_endpoint_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取端点统计数据。
        
        Returns:
            Dict[str, Dict[str, Any]]: 端点统计
        """
        with self._lock:
            return {
                endpoint: stats.to_dict()
                for endpoint, stats in self._endpoint_stats.items()
            }
    
    def get_recent_metrics(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取最近的请求指标。
        
        Args:
            limit: 限制返回数量
            
        Returns:
            List[Dict[str, Any]]: 最近的指标
        """
        with self._lock:
            metrics = list(self._recent_metrics)
            if limit:
                metrics = metrics[-limit:]
            
            return [
                {
                    "method": m.method,
                    "url": m.url,
                    "status_code": m.status_code,
                    "response_time": m.response_time,
                    "request_size": m.request_size,
                    "response_size": m.response_size,
                    "timestamp": m.timestamp,
                    "success": m.success,
                    "error_type": m.error_type
                }
                for m in metrics
            ]
    
    def get_slow_requests(self, threshold: float = PerformanceConstants.SLOW_REQUEST_THRESHOLD) -> List[Dict[str, Any]]:
        """
        获取慢请求列表。
        
        Args:
            threshold: 慢请求阈值（秒）
            
        Returns:
            List[Dict[str, Any]]: 慢请求列表
        """
        with self._lock:
            slow_requests = []
            for metric in self._recent_metrics:
                if metric.response_time > threshold:
                    slow_requests.append({
                        "method": metric.method,
                        "url": metric.url,
                        "response_time": metric.response_time,
                        "timestamp": metric.timestamp,
                        "status_code": metric.status_code
                    })
            
            # 按响应时间降序排序
            slow_requests.sort(key=lambda x: x["response_time"], reverse=True)
            return slow_requests
    
    def reset_stats(self) -> None:
        """重置所有统计数据。"""
        with self._lock:
            self._recent_metrics.clear()
            self._global_stats = PerformanceStats()
            self._endpoint_stats.clear()
            self._window_stats = PerformanceStats()
            self._start_time = time.time()
            
            self.logger.info("性能统计数据已重置")


# 全局指标收集器实例
_global_collector: Optional[MetricsCollector] = None
_collector_lock = threading.Lock()


def get_metrics_collector() -> MetricsCollector:
    """获取全局指标收集器实例。"""
    global _global_collector
    
    if _global_collector is None:
        with _collector_lock:
            if _global_collector is None:
                _global_collector = MetricsCollector()
    
    return _global_collector


def record_request_metrics(
    method: str,
    url: str,
    status_code: int,
    response_time: float,
    request_size: int = 0,
    response_size: int = 0,
    success: bool = True,
    error_type: Optional[str] = None
) -> None:
    """
    记录请求指标的便捷函数。
    
    Args:
        method: HTTP方法
        url: 请求URL
        status_code: 状态码
        response_time: 响应时间
        request_size: 请求大小
        response_size: 响应大小
        success: 是否成功
        error_type: 错误类型
    """
    metric = RequestMetric(
        method=method,
        url=url,
        status_code=status_code,
        response_time=response_time,
        request_size=request_size,
        response_size=response_size,
        timestamp=time.time(),
        success=success,
        error_type=error_type
    )
    
    collector = get_metrics_collector()
    collector.record_request(metric)


def metrics_decorator(func: Callable) -> Callable:
    """
    性能指标装饰器。
    
    Args:
        func: 被装饰的函数
        
    Returns:
        Callable: 装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        error_type = None
        
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            success = False
            error_type = type(e).__name__
            raise
        finally:
            response_time = time.time() - start_time
            
            # 记录指标
            record_request_metrics(
                method=func.__name__,
                url=getattr(func, '__module__', 'unknown'),
                status_code=200 if success else 500,
                response_time=response_time,
                success=success,
                error_type=error_type
            )
    
    return wrapper
