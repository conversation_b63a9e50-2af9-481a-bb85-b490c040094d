"""
Mail Verifier Package.

This package provides a client to interact with mail.tm API for fetching emails and verification codes.
"""
from .client import MailVerifierClient
from .verification_parser import VerificationCodeParser
from .config import MailVerifierConfig, get_config, set_config, load_config
from .exceptions import (
    MailVerifierError, ConfigurationError, AuthenticationError, AuthorizationError,
    RateLimitError, NetworkError, TimeoutError, APIError, ValidationError,
    ParsingError, SecurityError, ResourceError, MessageNotFoundError,
    VerificationCodeNotFoundError, RetryExhaustedError
)
from .logging import get_logger, setup_logging
from .validators import validate_and_clean_message, clean_email_content, validate_url_safe
from .cache import get_message_cache, get_parse_cache, cleanup_all_caches
from .polling import SmartPoller, PollingStrategy, create_smart_poller
from .security import SecureSession, RequestSigner, create_secure_session
from .health import <PERSON><PERSON>he<PERSON>, HealthStatus, SystemHealth, create_health_checker
from .metrics import MetricsCollector, get_metrics_collector, record_request_metrics, metrics_decorator
from .memory import MemoryMonitor, get_memory_monitor, check_memory_usage, cleanup_memory, memory_limit_decorator
from . import constants, types

__all__ = [
    # 核心类
    "MailVerifierClient",
    "VerificationCodeParser",
    "MailVerifierConfig",

    # 配置管理
    "get_config",
    "set_config",
    "load_config",

    # 日志管理
    "get_logger",
    "setup_logging",

    # 验证和清理
    "validate_and_clean_message",
    "clean_email_content",
    "validate_url_safe",

    # 缓存管理
    "get_message_cache",
    "get_parse_cache",
    "cleanup_all_caches",

    # 智能轮询
    "SmartPoller",
    "PollingStrategy",
    "create_smart_poller",

    # 网络安全
    "SecureSession",
    "RequestSigner",
    "create_secure_session",

    # 健康检查
    "HealthChecker",
    "HealthStatus",
    "SystemHealth",
    "create_health_checker",

    # 性能指标
    "MetricsCollector",
    "get_metrics_collector",
    "record_request_metrics",
    "metrics_decorator",

    # 内存管理
    "MemoryMonitor",
    "get_memory_monitor",
    "check_memory_usage",
    "cleanup_memory",
    "memory_limit_decorator",

    # 类型定义
    "types",
    "constants",

    # 异常类
    "MailVerifierError",
    "ConfigurationError",
    "AuthenticationError",
    "AuthorizationError",
    "RateLimitError",
    "NetworkError",
    "TimeoutError",
    "APIError",
    "ValidationError",
    "ParsingError",
    "SecurityError",
    "ResourceError",
    "MessageNotFoundError",
    "VerificationCodeNotFoundError",
    "RetryExhaustedError"
]
