[project]
name = "mail-verifier"
version = "0.1.0"
description = "A modern mail verifier tool with enhanced verification code parsing"
readme = "README.md"
requires-python = ">=3.8"
license = { text = "MIT" }
authors = [
  { name="Mail Verifier Team", email="<EMAIL>" },
]
keywords = ["mail", "verification", "email", "code", "parser"]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: MIT License",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Topic :: Communications :: Email",
  "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
  "requests>=2.25.0,<3.0.0",
  "structlog>=23.0.0,<25.0.0",
]

[project.optional-dependencies]
dev = [
  "pytest>=7.0.0,<9.0.0",
  "pytest-cov>=4.0.0,<6.0.0",
  "requests-mock>=1.9.0,<2.0.0",
  "ruff>=0.1.0,<1.0.0",
  "mypy>=1.0.0,<2.0.0",
]
test = [
  "pytest>=7.0.0,<9.0.0",
  "pytest-cov>=4.0.0,<6.0.0",
  "requests-mock>=1.9.0,<2.0.0",
]
lint = [
  "ruff>=0.1.0,<1.0.0",
  "mypy>=1.0.0,<2.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mail_verifier"]

# Ruff configuration
[tool.ruff]
line-length = 88
target-version = "py38"
src = ["src", "tests"]

[tool.ruff.lint]
select = [
  "E",   # pycodestyle errors
  "W",   # pycodestyle warnings
  "F",   # pyflakes
  "I",   # isort
  "B",   # flake8-bugbear
  "C4",  # flake8-comprehensions
  "UP",  # pyupgrade
  "ARG", # flake8-unused-arguments
  "SIM", # flake8-simplify
  "TCH", # flake8-type-checking
  "PTH", # flake8-use-pathlib
]
ignore = [
  "E501",  # line too long, handled by black
  "B008",  # do not perform function calls in argument defaults
  "C901",  # too complex
]

[tool.ruff.lint.per-file-ignores]
"tests/**/*" = ["ARG", "S101"]

[tool.ruff.lint.isort]
known-first-party = ["mail_verifier"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
  "requests.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
  "--strict-markers",
  "--strict-config",
  "--cov=mail_verifier",
  "--cov-report=term-missing",
  "--cov-report=html",
  "--cov-report=xml",
]
testpaths = ["tests"]
markers = [
  "slow: marks tests as slow (deselect with '-m \"not slow\"')",
  "integration: marks tests as integration tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
branch = true

[tool.coverage.report]
exclude_lines = [
  "pragma: no cover",
  "def __repr__",
  "if self.debug:",
  "if settings.DEBUG",
  "raise AssertionError",
  "raise NotImplementedError",
  "if 0:",
  "if __name__ == .__main__.:",
  "class .*\\bProtocol\\):",
  "@(abc\\.)?abstractmethod",
]
