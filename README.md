# Mail Verifier

一个用于与 `mail.tm` 交互以自动获取邮件验证码的 Python 客户端。

## 特性

*   **面向对象的客户端**: 简单易用的 API。
*   **自动轮询**: 自动等待并获取最新的邮件。
*   **可配置**: 可自定义超时时间和轮询间隔。
*   **轻量级**: 依赖少，易于集成。
*   **现代化**: 使用 uv 包管理器，提供极速的依赖解析和安装。

## 安装

推荐使用 `uv`（现代化的Python包管理器）来安装这个包，它比传统的 `pip` 快10-100倍。

### 使用 uv（推荐）

```bash
# 安装 uv（如果还没有安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装项目
uv sync

# 或者直接安装到当前环境
uv pip install .
```

如果你需要进行开发，uv 会自动管理开发依赖：

```bash
# 初始化开发环境（包含所有开发依赖）
uv sync --group dev

# 或者手动安装开发依赖
uv pip install .[dev]
```

### 使用传统 pip

如果你更喜欢使用传统的 pip：

```bash
# 安装核心库
pip install .

# 安装开发依赖
pip install .[dev]
```

## 快速上手/用法

下面是一个如何使用 `MailVerifierClient` 来获取最新邮件验证码的示例。

```python
from mail_verifier import MailVerifierClient
import os

# 从环境变量或安全的地方获取你的API令牌
# 注意：不要将令牌硬编码在代码中
API_TOKEN = os.environ.get("MAIL_TM_TOKEN", "your-default-token")

def main():
    # 初始化客户端
    client = MailVerifierClient(token=API_TOKEN)
    print("正在等待新的邮件验证码...")

    # 获取最新的验证码，最长等待60秒
    verification_code = client.get_latest_verification_code(timeout=60)

    if verification_code:
        print(f"成功获取验证码: {verification_code}")
    else:
        print("在超时时间内未找到验证码。")

if __name__ == "__main__":
    main()
```

## 运行测试

### 使用 uv（推荐）

```bash
# 运行测试（uv 会自动管理测试环境）
uv run pytest

# 运行测试并生成覆盖率报告
uv run pytest --cov=mail_verifier

# 运行特定测试文件
uv run pytest tests/test_client.py -v
```

### 使用传统方式

要运行测试套件，请确保已安装开发依赖，然后运行：

```bash
pytest
```

## 开发工作流程

本项目使用现代化的Python开发工具链，推荐以下开发工作流程：

### 1. 环境设置

```bash
# 克隆项目
git clone <repository-url>
cd mail-verifier

# 使用 uv 设置开发环境
uv sync --group dev
```

### 2. 代码质量检查

```bash
# 代码格式化
uv run ruff format

# 代码检查
uv run ruff check

# 类型检查
uv run mypy src/

# 运行所有检查
uv run ruff check && uv run mypy src/
```

### 3. 测试

```bash
# 运行所有测试
uv run pytest

# 运行测试并生成覆盖率报告
uv run pytest --cov=mail_verifier --cov-report=html

# 运行特定测试
uv run pytest tests/test_client.py::test_client_initialization -v
```

### 4. 依赖管理

```bash
# 添加新的生产依赖
uv add package-name

# 添加开发依赖
uv add --group dev package-name

# 更新依赖
uv sync

# 查看依赖树
uv tree
```

## 项目特性

### 🚀 现代化技术栈

- **uv**: 极速的Python包管理器
- **Ruff**: 快速的代码检查和格式化工具
- **MyPy**: 静态类型检查
- **Pytest**: 现代化的测试框架
- **Hatchling**: 现代化的构建后端

### 📦 依赖分组管理

项目使用现代化的依赖分组管理（通过optional-dependencies实现）：

- `dev`: 完整的开发环境依赖
- `test`: 仅测试相关依赖
- `lint`: 仅代码检查相关依赖

> **注意**: 当uv升级到支持dependency-groups的版本时，我们将迁移到更现代的dependency-groups语法。