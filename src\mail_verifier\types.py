"""
类型定义模块。

定义项目中使用的复杂数据结构的类型注解。
"""
import sys
from typing import Any, Dict, List, Optional, Union

# Python 3.8+ 兼容性
if sys.version_info >= (3, 8):
    from typing import TypedDict, Literal, Protocol
else:
    from typing_extensions import TypedDict, Literal, Protocol


# API响应类型
class MessageData(TypedDict, total=False):
    """邮件消息数据类型。"""
    id: str
    accountId: str
    msgid: str
    from: Dict[str, str]
    to: List[Dict[str, str]]
    subject: str
    intro: str
    seen: bool
    isDeleted: bool
    hasAttachments: bool
    size: int
    downloadUrl: str
    createdAt: str
    updatedAt: str
    text: Optional[str]
    html: Optional[str]


class HydraMember(TypedDict):
    """Hydra API响应成员类型。"""
    hydra_member: List[MessageData]


class APIResponse(TypedDict, total=False):
    """通用API响应类型。"""
    hydra_member: List[MessageData]
    hydra_totalItems: int
    hydra_view: Dict[str, Any]


# 验证码解析类型
class VerificationCodeMatch(TypedDict):
    """验证码匹配结果类型。"""
    code: str
    score: int
    context: str
    pattern_name: str
    position: int


class ParseResult(TypedDict):
    """解析结果类型。"""
    success: bool
    code: Optional[str]
    confidence: float
    matches: List[VerificationCodeMatch]
    processing_time: float


# 缓存类型
class CacheStats(TypedDict):
    """缓存统计信息类型。"""
    hits: int
    misses: int
    evictions: int
    expired: int
    size: int
    max_size: int
    hit_rate: float


class CacheEntry(TypedDict):
    """缓存条目类型。"""
    value: Any
    timestamp: float
    ttl: int


# 轮询类型
class PollingStats(TypedDict):
    """轮询统计信息类型。"""
    total_polls: int
    successful_polls: int
    failed_polls: int
    total_time: float
    avg_interval: float
    success_rate: float
    avg_time_to_success: float
    consecutive_failures: int
    consecutive_successes: int
    strategy: str


class PollingConfig(TypedDict):
    """轮询配置类型。"""
    strategy: str
    initial_interval: float
    max_interval: float
    backoff_factor: float
    jitter: bool
    adaptive_threshold: int


# 日志类型
class LogEntry(TypedDict, total=False):
    """日志条目类型。"""
    timestamp: str
    level: str
    logger: str
    message: str
    module: str
    function: str
    line: int
    extra_data: Dict[str, Any]
    exception: Optional[str]


class AuditLogEntry(TypedDict):
    """审计日志条目类型。"""
    audit: bool
    action: str
    timestamp: float
    user_id: Optional[str]
    resource_type: Optional[str]
    resource_id: Optional[str]
    details: Dict[str, Any]


# 配置类型
class NetworkConfig(TypedDict):
    """网络配置类型。"""
    request_timeout: int
    max_retries: int
    retry_backoff_factor: float
    ssl_verify: bool


class CacheConfig(TypedDict):
    """缓存配置类型。"""
    enable_cache: bool
    cache_size: int
    cache_ttl: int


class LoggingConfig(TypedDict):
    """日志配置类型。"""
    log_level: str
    log_format: str
    enable_audit_log: bool


class SecurityConfig(TypedDict):
    """安全配置类型。"""
    token_validation_enabled: bool
    max_email_content_size: int
    regex_timeout: float


# 验证类型
class ValidationResult(TypedDict):
    """验证结果类型。"""
    is_valid: bool
    cleaned_data: Any
    errors: List[str]
    warnings: List[str]


class ValidationError(TypedDict):
    """验证错误类型。"""
    field: str
    value: Any
    message: str
    code: str


# 性能监控类型
class PerformanceMetrics(TypedDict):
    """性能指标类型。"""
    request_count: int
    success_count: int
    error_count: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    total_time: float
    memory_usage: int
    cache_hit_rate: float


class RequestMetrics(TypedDict):
    """请求指标类型。"""
    method: str
    url: str
    status_code: int
    response_time: float
    request_size: int
    response_size: int
    timestamp: float


# 错误类型
class ErrorDetails(TypedDict, total=False):
    """错误详细信息类型。"""
    error_code: str
    error_message: str
    error_type: str
    stack_trace: Optional[str]
    context: Dict[str, Any]
    timestamp: float
    request_id: Optional[str]


# 协议类型
class Cacheable(Protocol):
    """可缓存对象协议。"""
    
    def get_cache_key(self) -> str:
        """获取缓存键。"""
        ...
    
    def get_cache_ttl(self) -> int:
        """获取缓存TTL。"""
        ...


class Loggable(Protocol):
    """可记录日志对象协议。"""
    
    def get_log_data(self) -> Dict[str, Any]:
        """获取日志数据。"""
        ...


class Validatable(Protocol):
    """可验证对象协议。"""
    
    def validate(self) -> ValidationResult:
        """验证对象。"""
        ...


# 联合类型
MessageContent = Union[str, bytes]
CacheValue = Union[str, int, float, bool, Dict[str, Any], List[Any]]
ConfigValue = Union[str, int, float, bool]

# 字面量类型
LogLevel = Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
LogFormat = Literal["json", "text"]
PollingStrategy = Literal["fixed", "exponential_backoff", "adaptive", "fibonacci"]
ContentType = Literal["text", "html"]

# 回调类型
from typing import Callable

CheckFunction = Callable[[], Any]
SuccessCondition = Callable[[Any], bool]
CacheKeyFunction = Callable[..., str]
ValidationFunction = Callable[[Any], ValidationResult]
LogFormatter = Callable[[LogEntry], str]

# 异常类型映射
ExceptionMapping = Dict[int, type]
ErrorHandlerMapping = Dict[type, Callable[[Exception], Any]]
