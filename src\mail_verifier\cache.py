"""
缓存模块。

提供多层缓存机制，包括内存缓存、LRU缓存和TTL缓存。
支持消息缓存、解析结果缓存等功能。
"""
import time
import hashlib
import threading
from typing import Any, Dict, Optional, Tuple, Union, Callable
from functools import wraps
from collections import OrderedDict

from .config import get_config
from .logging import get_logger


class TTLCache:
    """带TTL（生存时间）的缓存。"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        初始化TTL缓存。
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认TTL（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict = OrderedDict()
        self._timestamps: Dict[str, float] = {}
        self._lock = threading.RLock()
        self.logger = get_logger("cache")
        
        # 统计信息
        self._stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expired": 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值。
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        with self._lock:
            if key not in self._cache:
                self._stats["misses"] += 1
                return None
            
            # 检查是否过期
            if self._is_expired(key):
                self._remove(key)
                self._stats["expired"] += 1
                self._stats["misses"] += 1
                return None
            
            # 移动到末尾（LRU）
            value = self._cache.pop(key)
            self._cache[key] = value
            self._stats["hits"] += 1
            
            return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        设置缓存值。
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），None使用默认值
        """
        with self._lock:
            ttl = ttl or self.default_ttl
            
            # 如果键已存在，更新值和时间戳
            if key in self._cache:
                self._cache.pop(key)
            
            # 检查缓存大小限制
            while len(self._cache) >= self.max_size:
                self._evict_oldest()
            
            self._cache[key] = value
            self._timestamps[key] = time.time() + ttl
    
    def delete(self, key: str) -> bool:
        """
        删除缓存条目。
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        with self._lock:
            if key in self._cache:
                self._remove(key)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存。"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
            self.logger.info("缓存已清空")
    
    def cleanup_expired(self) -> int:
        """
        清理过期条目。
        
        Returns:
            清理的条目数
        """
        with self._lock:
            expired_keys = []
            current_time = time.time()
            
            for key, expire_time in self._timestamps.items():
                if current_time > expire_time:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove(key)
                self._stats["expired"] += 1
            
            if expired_keys:
                self.logger.debug(f"清理了{len(expired_keys)}个过期缓存条目")
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息。"""
        with self._lock:
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = self._stats["hits"] / total_requests if total_requests > 0 else 0
            
            return {
                **self._stats,
                "size": len(self._cache),
                "max_size": self.max_size,
                "hit_rate": hit_rate
            }
    
    def _is_expired(self, key: str) -> bool:
        """检查键是否过期。"""
        if key not in self._timestamps:
            return True
        return time.time() > self._timestamps[key]
    
    def _remove(self, key: str) -> None:
        """移除缓存条目。"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
    
    def _evict_oldest(self) -> None:
        """驱逐最旧的条目。"""
        if self._cache:
            oldest_key = next(iter(self._cache))
            self._remove(oldest_key)
            self._stats["evictions"] += 1


class MessageCache:
    """消息缓存，专门用于缓存API响应的消息数据。"""
    
    def __init__(self):
        """初始化消息缓存。"""
        config = get_config()
        self.cache = TTLCache(
            max_size=config.cache_size,
            default_ttl=config.cache_ttl
        )
        self.logger = get_logger("message_cache")
    
    def get_messages(self, cache_key: str) -> Optional[list]:
        """
        获取缓存的消息列表。
        
        Args:
            cache_key: 缓存键
            
        Returns:
            消息列表或None
        """
        messages = self.cache.get(cache_key)
        if messages is not None:
            self.logger.debug("消息列表缓存命中", cache_key=cache_key, count=len(messages))
        return messages
    
    def set_messages(self, cache_key: str, messages: list, ttl: Optional[int] = None) -> None:
        """
        缓存消息列表。
        
        Args:
            cache_key: 缓存键
            messages: 消息列表
            ttl: 生存时间
        """
        self.cache.set(cache_key, messages, ttl)
        self.logger.debug("消息列表已缓存", cache_key=cache_key, count=len(messages))
    
    def get_message(self, message_id: str) -> Optional[dict]:
        """
        获取缓存的单个消息。
        
        Args:
            message_id: 消息ID
            
        Returns:
            消息数据或None
        """
        message = self.cache.get(f"msg:{message_id}")
        if message is not None:
            self.logger.debug("消息缓存命中", message_id=message_id)
        return message
    
    def set_message(self, message_id: str, message: dict, ttl: Optional[int] = None) -> None:
        """
        缓存单个消息。
        
        Args:
            message_id: 消息ID
            message: 消息数据
            ttl: 生存时间
        """
        self.cache.set(f"msg:{message_id}", message, ttl)
        self.logger.debug("消息已缓存", message_id=message_id)
    
    def invalidate_message(self, message_id: str) -> None:
        """
        使消息缓存失效。
        
        Args:
            message_id: 消息ID
        """
        if self.cache.delete(f"msg:{message_id}"):
            self.logger.debug("消息缓存已失效", message_id=message_id)
    
    def cleanup(self) -> None:
        """清理过期缓存。"""
        expired_count = self.cache.cleanup_expired()
        if expired_count > 0:
            self.logger.info("清理了过期的消息缓存", count=expired_count)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息。"""
        return self.cache.get_stats()


class ParseResultCache:
    """解析结果缓存，用于缓存验证码解析结果。"""
    
    def __init__(self):
        """初始化解析结果缓存。"""
        config = get_config()
        self.cache = TTLCache(
            max_size=config.cache_size // 2,  # 解析缓存使用一半的缓存大小
            default_ttl=config.cache_ttl * 2  # 解析结果缓存时间更长
        )
        self.logger = get_logger("parse_cache")
    
    def get_parse_result(self, content: str) -> Optional[str]:
        """
        获取缓存的解析结果。
        
        Args:
            content: 邮件内容
            
        Returns:
            解析结果或None
        """
        # 使用内容的哈希作为缓存键
        cache_key = self._get_content_hash(content)
        result = self.cache.get(cache_key)
        
        if result is not None:
            self.logger.debug("解析结果缓存命中", cache_key=cache_key[:8])
        
        return result
    
    def set_parse_result(self, content: str, result: str, ttl: Optional[int] = None) -> None:
        """
        缓存解析结果。
        
        Args:
            content: 邮件内容
            result: 解析结果
            ttl: 生存时间
        """
        cache_key = self._get_content_hash(content)
        self.cache.set(cache_key, result, ttl)
        self.logger.debug("解析结果已缓存", cache_key=cache_key[:8], result=result)
    
    def _get_content_hash(self, content: str) -> str:
        """
        获取内容的哈希值。
        
        Args:
            content: 内容
            
        Returns:
            哈希值
        """
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息。"""
        return self.cache.get_stats()


# 全局缓存实例
_message_cache: Optional[MessageCache] = None
_parse_cache: Optional[ParseResultCache] = None


def get_message_cache() -> MessageCache:
    """获取消息缓存实例。"""
    global _message_cache
    if _message_cache is None:
        _message_cache = MessageCache()
    return _message_cache


def get_parse_cache() -> ParseResultCache:
    """获取解析缓存实例。"""
    global _parse_cache
    if _parse_cache is None:
        _parse_cache = ParseResultCache()
    return _parse_cache


def cached_api_call(cache_key_func: Callable = None, ttl: Optional[int] = None):
    """
    API调用缓存装饰器。
    
    Args:
        cache_key_func: 生成缓存键的函数
        ttl: 缓存生存时间
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cache = get_message_cache()
            result = cache.cache.get(cache_key)
            
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


def cleanup_all_caches() -> None:
    """清理所有缓存。"""
    logger = get_logger("cache")
    
    if _message_cache:
        _message_cache.cleanup()
    
    if _parse_cache:
        _parse_cache.cache.cleanup_expired()
    
    logger.info("所有缓存清理完成")
